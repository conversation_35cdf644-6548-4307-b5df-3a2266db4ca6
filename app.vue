<template>
  <NuxtLayout>
    <NuxtPage />
  </NuxtLayout>
</template>

<script setup lang="ts">
import { useAnimations } from '~/composables/useAnimations';

// Set up page transitions
const { pageTransitionEnter, pageTransitionLeave } = useAnimations();

// Page transition configuration
const pageTransition = {
  name: 'page',
  mode: 'out-in',
  onEnter: (el: Element, done: () => void) => {
    pageTransitionEnter(el).then(done);
  },
  onLeave: (el: Element, done: () => void) => {
    pageTransitionLeave(el).then(done);
  }
};

// Apply page transition
useHead({
  bodyAttrs: {
    class: 'page-transitions-enabled'
  }
});
</script>

<style>
/* Global page transition styles */
.page-transitions-enabled {
  overflow-x: hidden;
}

/* Ensure smooth transitions */
.page-enter-active,
.page-leave-active {
  transition: all 0.4s ease-in-out;
}

.page-enter-from {
  opacity: 0;
  transform: translateY(20px) scale(0.98);
}

.page-leave-to {
  opacity: 0;
  transform: translateY(-20px) scale(1.02);
}

/* Enhanced button and interactive element styles */
button,
a,
.interactive {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Smooth scroll behavior */
html {
  scroll-behavior: smooth;
}

/* Performance optimizations for animations */
.transform-gpu {
  transform: translateZ(0);
  backface-visibility: hidden;
  perspective: 1000px;
}

/* Loading animation keyframes */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.animate-shimmer {
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
}

/* Reduced motion preferences */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}
</style>
