<template>
  <div class="bg-white p-6 md:p-8 lg:p-6 rounded-lg shadow-md">
    <h2 class="text-2xl md:text-3xl lg:text-2xl font-bold mb-6 md:mb-8 lg:mb-6">{{ title }}</h2>
    
    <form @submit.prevent="handleSubmit" class="space-y-6 md:space-y-8 lg:space-y-6">
      <FormGroup
        v-model="form.name"
        label="Name"
        placeholder="Enter your name"
        required
        :error="errors.name"
        :show-button="false"
        class="md:mb-2"
      />
      
      <FormGroup
        v-model="form.email"
        label="Email"
        type="email"
        placeholder="Enter your email"
        required
        :error="errors.email"
        :show-button="false"
        class="md:mb-2"
      />
      
      <FormGroup
        v-model="form.message"
        label="Message"
        placeholder="Enter your message"
        required
        :error="errors.message"
        :show-button="false"
      >
        <template #input>
          <textarea
            v-model="form.message"
            class="w-full px-3 md:px-4 lg:px-3 py-2 md:py-3 lg:py-2 border rounded-md focus:outline-none focus:ring-2 transition-all border-gray-300 focus:ring-blue-500 min-h-[120px] md:min-h-[150px] lg:min-h-[120px] text-base md:text-lg lg:text-base"
            placeholder="Enter your message"
            required
          ></textarea>
        </template>
      </FormGroup>
      
      <div class="flex justify-end">
        <Button
          type="submit"
          :disabled="isSubmitting"
          :variant="submitButtonVariant"
          size="md"
          class="min-w-[120px] md:min-w-[150px] lg:min-w-[120px]"
        >
          {{ isSubmitting ? 'Sending...' : submitButtonText }}
        </Button>
      </div>
      
      <p v-if="successMessage" class="text-green-600 mt-4 md:mt-6 lg:mt-4 text-base md:text-lg lg:text-base">{{ successMessage }}</p>
    </form>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue';
import FormGroup from '../molecules/FormGroup.vue';
import Button from '../atoms/Button.vue';

const props = defineProps({
  title: {
    type: String,
    default: 'Contact Us'
  },
  submitButtonText: {
    type: String,
    default: 'Send Message'
  },
  submitButtonVariant: {
    type: String as () => 'primary' | 'secondary' | 'outline',
    default: 'primary'
  }
});

const emit = defineEmits(['submit', 'success']);

const form = reactive({
  name: '',
  email: '',
  message: ''
});

const errors = reactive({
  name: '',
  email: '',
  message: ''
});

const isSubmitting = ref(false);
const successMessage = ref('');

const validateForm = () => {
  let isValid = true;
  
  // Reset errors
  errors.name = '';
  errors.email = '';
  errors.message = '';
  
  if (!form.name.trim()) {
    errors.name = 'Name is required';
    isValid = false;
  }
  
  if (!form.email.trim()) {
    errors.email = 'Email is required';
    isValid = false;
  } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(form.email)) {
    errors.email = 'Please enter a valid email address';
    isValid = false;
  }
  
  if (!form.message.trim()) {
    errors.message = 'Message is required';
    isValid = false;
  }
  
  return isValid;
};

const handleSubmit = async () => {
  if (!validateForm()) return;
  
  isSubmitting.value = true;
  successMessage.value = '';
  
  try {
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    emit('submit', { ...form });
    
    // Reset form after successful submission
    form.name = '';
    form.email = '';
    form.message = '';
    
    successMessage.value = 'Your message has been sent successfully!';
    emit('success');
  } catch (error) {
    console.error('Error submitting form:', error);
  } finally {
    isSubmitting.value = false;
  }
};
</script>