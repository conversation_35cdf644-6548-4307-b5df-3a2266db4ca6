<template>
  <div class="py-12 md:py-20">
    <div>
      <!-- Section Heading - Moved outside the flex layout -->
      <h3 class="h3 text-neutral-7 mb-8 md:mb-10 lg:mb-12 text-left">Professional Experience</h3>
      
      <div class="flex flex-col md:flex-row gap-8 md:gap-16 lg:gap-24">
        <!-- Tabs Section -->
        <div class="w-full md:w-1/4 flex flex-col items-start md:items-end">
          <ul class="flex flex-row md:flex-col gap-4 md:gap-8 w-full overflow-x-auto md:overflow-x-visible pb-4 md:pb-0 md:snap-none snap-x snap-mandatory scrollbar-hide">
            <li v-for="exp in experiences" :key="exp.period" class="snap-start">
              <button
                @click="selectedPeriod = exp.period"
                :class="[
                  'body-2-bold whitespace-nowrap py-2 md:py-3 px-4 md:px-0',
                  selectedPeriod === exp.period ? 'text-neutral-7 border-b-2 border-primary-2' : 'text-neutral-3 hover:text-neutral-5'
                ]"
              >
                {{ exp.period }}
              </button>
            </li>
          </ul>
        </div>

        <!-- Content Section -->
        <div class="w-full md:w-3/4 relative">
          <Transition
            name="experience-fade"
            mode="out-in"
            @before-enter="onBeforeEnter"
            @enter="onEnter"
            @leave="onLeave"
          >
            <div v-if="selectedExperience" :key="selectedPeriod" class="bg-neutral-1 md:bg-transparent p-0 md:p-0 rounded-lg">
              <!-- Main Info Card -->
              <div class="bg-gradient-to-br from-neutral-1 via-primary-2/70 to-primary-3/80 p-5 md:p-8 lg:p-12 rounded-xl mb-6">
                <h4 class="h5-bold text-primary-3 mb-2">{{ selectedExperience.title }} at {{ selectedExperience.company }}</h4>
                <p class="body-2-regular text-neutral-7 mb-6">Role: {{ selectedExperience.role }}</p>

                <div class="space-y-3">
                  <div v-for="(techList, category) in filteredTechnologies" :key="category" class="technology-item">
                    <p class="body-2-regular text-neutral-7">
                      <span class="capitalize font-semibold">{{ formatCategoryName(category) }}: </span>{{ techList }}
                    </p>
                  </div>
                </div>
              </div>

              <!-- Details & Achievements Cards -->
              <div class="grid grid-cols-1 md:grid-cols-1 lg:grid-cols-2 gap-6" :class="{
                'lg:grid-cols-1': (!selectedExperience.details || selectedExperience.details.length === 0) || 
                                  (!selectedExperience.achievements || selectedExperience.achievements.length === 0)
              }">
                <!-- Details Card - Only show if there are details -->
                <div v-if="selectedExperience.details && selectedExperience.details.length > 0" class="bg-primary-2 p-5 md:p-6 rounded-xl">
                  <div class="flex items-center gap-3 mb-4 md:mb-6">
                    <!-- Suitcase Icon Placeholder -->
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="text-neutral-7"><path d="M16 20V4a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16"></path><rect width="20" height="14" x="2" y="6" rx="2"></rect></svg>
                    <h5 class="body-1-semibold text-neutral-7">Details</h5>
                  </div>
                  <ul class="space-y-3">
                    <li v-for="(detail, index) in selectedExperience.details" :key="index" class="body-2-regular text-neutral-7">
                      - {{ detail }}
                    </li>
                  </ul>
                </div>

                <!-- Achievements Card - Only show if there are achievements -->
                <div v-if="selectedExperience.achievements && selectedExperience.achievements.length > 0" class="bg-neutral-2 p-5 md:p-6 rounded-xl">
                  <div class="flex items-center gap-3 mb-4 md:mb-6">
                    <!-- Podium Icon Placeholder -->
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="text-neutral-7"><path d="M12 22V8"></path><path d="M5 8H2a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1h3"></path><path d="M19 4h3a1 1 0 0 1 1 1v3a1 1 0 0 1-1 1h-3"></path><path d="M5 12H2a1 1 0 0 1-1-1V8a1 1 0 0 1 1-1h3"></path><path d="M19 8h3a1 1 0 0 1 1 1v3a1 1 0 0 1-1 1h-3"></path><path d="M10 12H8a1 1 0 0 1-1-1V8a1 1 0 0 1 1-1h2"></path><path d="M16 8h-2a1 1 0 0 0-1 1v3a1 1 0 0 0 1 1h2"></path></svg>
                    <h5 class="body-1-semibold text-neutral-7">Achievements</h5>
                  </div>
                  <ul class="space-y-3">
                    <li v-for="(achievement, index) in selectedExperience.achievements" :key="index" class="body-2-regular text-neutral-7">
                      - {{ achievement }}
                    </li>
                  </ul>
                </div>
              </div>
            </div>
            <div v-else :key="'empty-state'" class="text-neutral-5 body-1-regular">
              Select a period to see details.
            </div>
          </Transition>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';

interface TechnologyDetails {
  backend: string;
  frontend: string;
  platform: string;
  database: string;
  devops: string;
  bundlers: string;
  templateEngines: string;
}

interface Experience {
  period: string;
  title: string;
  company: string;
  role: string;
  technologies: TechnologyDetails;
  details: string[];
  achievements: string[];
}

const experiences = ref<Experience[]>([
  {
    period: "2022 - Present",
    title: "Technical Lead",
    company: "TechVSI, Vietnam",
    role: "Technical Lead, Full Stack Developer",
    technologies: {
      backend: "NodeJS, NestJS, PHP, Laravel",
      frontend: "HTML5, CSS3, SCSS, Bootstrap, TailwindCSS, JQuery, VanillaJS, ReactJS, Liquid, SvelteJS, SapperJS, SvelteKit, VueJS, NextJS, RemixJS, GatsbyJS",
      platform: "WordPress, Shopify, Directus, Ghost, Strapi, Contentful, and KeystoneJS",
      database: "MongoDB, MySQL, Postgres, Redis, Firebase Realtime Database",
      devops: "AWS, Tencent, Huawei, Alibaba, Google Cloud, DigitalOcean, Vercel, GitLab CI/CD, MongoAtlas",
      bundlers: "Webpack, Rollup, Vite, Gulp",
      templateEngines: "Blade Template, Nunjunks, Twig, EJS, Handlebars"
    },
    details: [
      "Lead a development team to handle a variety of projects",
      "Manage timeline and client expectation",
      "Planning and solution architecture",
      "Apply new processes to the daily improvement of the development team",
      "Using new technologies to solve business requirements"
    ],
    achievements: [
      "Worked with team members and clients to developing Singapore's first Employment Marketplace named WerkSG with 52K+ users",
      "Worked with team members and client to developing Parkstan Livestream Application and admin management portal named RoccoVideo with 93K+ users"
    ]
  },
  {
    period: "Nov 2020 - 2022",
    title: "WordPress Developer",
    company: "TechVSI, Vietnam",
    role: "Lead Developer, Full Stack Developer",
    technologies: {
      backend: "PHP, NodeJS",
      frontend: "HTML5, CSS3, SCSS, Bootstrap, JQuery, VanillaJS, ReactJS, Liquid, SvelteJS, SapperJS, SvelteKit, TailwindCSS",
      platform: "WordPress, Shopify",
      database: "MySQL",
      devops: "AWS, Google Cloud, GitLab CI/CD",
      bundlers: "Webpack, Gulp",
      templateEngines: "PHP Templates"
    },
    details: [
      "Lead a development team to handle a variety of projects",
      "Manage timeline and client expectation",
      "Planning and solution architecture",
      "Apply new processes to the daily improvement of the development team",
      "Using new technologies to solve business requirements"
    ],
    achievements: []
  },
  {
    period: "April 2020 - Nov 2020",
    title: "WordPress Developer",
    company: "BAP IT JSC, Vietnam",
    role: "WordPress developer",
    technologies: {
      backend: "PHP",
      frontend: "HTML5, CSS3, JavaScript",
      platform: "WordPress",
      database: "MySQL",
      devops: "",
      bundlers: "Webpack, Gulp",
      templateEngines: "PHP Templates"
    },
    details: [
      "Developed custom REST APIs using PHP in WordPress",
      "Utilized Webpack and Gulp to build source bundlers/compilers for UI assets (JS, SCSS, Images)",
      "Maintain web systems",
      "Program the company's internal websites using WordPress",
      "Develop and write plugins for company products",
      "Optimize websites to improve performance and speed",
      "Implement new features and functionality for company websites"
    ],
    achievements: []
  },
  {
    period: "Nov 2019 - April 2020",
    title: "WordPress Developer",
    company: "DIVA Group, Vietnam",
    role: "WordPress Developer",
    technologies: {
      backend: "PHP",
      frontend: "Javascript, HTML5, CSS3, Jquery, Bootstrap",
      platform: "WordPress",
      database: "MySQL",
      devops: "",
      bundlers: "",
      templateEngines: "PHP Templates"
    },
    details: [],
    achievements: []
  }
]);

const selectedPeriod = ref<string>(experiences.value.length > 0 ? experiences.value[0].period : '');

const selectedExperience = computed(() => {
  return experiences.value.find(exp => exp.period === selectedPeriod.value);
});

// Filter out empty technology categories for cleaner display
const filteredTechnologies = computed(() => {
  if (!selectedExperience.value) return {};
  
  const result: Partial<TechnologyDetails> = {};
  Object.entries(selectedExperience.value.technologies).forEach(([key, value]) => {
    if (value && value.trim() !== '') {
      result[key as keyof TechnologyDetails] = value;
    }
  });
  
  return result;
});

const formatCategoryName = (category: string) => {
  // Add spaces before uppercase letters and then capitalize
  return category.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase()).trim();
};

// Transition effects handled with CSS transitions

const onBeforeEnter = (el: Element) => {
  // Ensure el is an HTMLElement for style properties
  if (el instanceof HTMLElement) {
    el.style.opacity = '0';
    el.style.transform = 'translateY(50px) scale(0.95)';
  }
};

const onEnter = (el: Element, done: () => void) => {
  if (el instanceof HTMLElement) {
    el.style.transition = 'all 0.7s cubic-bezier(0.19, 1, 0.22, 1)';
    el.style.opacity = '1';
    el.style.transform = 'translateY(0) scale(1)';
    setTimeout(done, 700);
  } else {
    done(); // Call done if not an HTMLElement to prevent blocking
  }
};

const onLeave = (el: Element, done: () => void) => {
  if (el instanceof HTMLElement) {
    el.style.transition = 'all 0.4s cubic-bezier(0.55, 0.085, 0.68, 0.53)';
    el.style.opacity = '0';
    el.style.transform = 'translateY(-30px) scale(0.95)';
    setTimeout(done, 400);
  } else {
    done(); // Call done if not an HTMLElement
  }
};

</script>

<style scoped>
/* Custom scrollbar for horizontal tabs on mobile if needed */
.overflow-x-auto::-webkit-scrollbar {
  height: 4px;
}
.overflow-x-auto::-webkit-scrollbar-thumb {
  background-color: var(--color-neutral-4);
  border-radius: 20px;
}
.overflow-x-auto::-webkit-scrollbar-track {
  background-color: var(--color-neutral-2);
}

/* Hide scrollbar for cleaner mobile experience */
.scrollbar-hide {
  -ms-overflow-style: none;  /* IE and Edge */
  scrollbar-width: none;  /* Firefox */
}
.scrollbar-hide::-webkit-scrollbar {
  display: none;  /* Chrome, Safari and Opera */
}

/* Ensure the gradient is applied correctly. Tailwind might need explicit configuration for complex gradients. */
/* Using a simpler approach for the main info card background for broader compatibility. */
/* The Figma design uses a radial gradient which is complex. The bg-gradient-to-br is a linear approximation. */
/* For exact radial gradient, custom CSS in main.css or a style block might be needed. */

/* Responsive technology items */
@media (max-width: 768px) {
  .technology-item {
    margin-bottom: 0.5rem;
  }
}
</style>
