<template>
  <div ref="projectsContainer" class="container mx-auto px-4 py-8 md:py-12 lg:py-16">
    <h2 class="h5-bold md:h4-bold lg:h3 text-center md:text-left mb-8 md:mb-10 lg:mb-12">Outstanding Projects</h2>

    <!-- Desktop and Tablet Layout -->
    <div class="hidden md:block">
      <!-- Large Project Card (First Project) -->
      <div v-if="projects.length > 0" class="mb-4 md:mb-5 lg:mb-6">
        <ProjectCard
          :key="`project-0`"
          :name="projects[0].name"
          :category="projects[0].category"
          :image-url="projects[0].imageUrl"
          :url="projects[0].url"
          variant="large"
          :gradient="projects[0].gradient"
          :glass-effect="projects[0].glassEffect"
          :overlay-color="projects[0].overlayColor"
          :glass-bg-color="projects[0].glassBgColor"
          @view-more="handleProjectClick"
        />
      </div>

      <!-- Smaller Project Cards Grid (Remaining Projects) -->
      <div v-if="projects.length > 1" class="grid md:grid-cols-1 lg:grid-cols-2 gap-4 md:gap-5 lg:gap-6">
        <ProjectCard
          v-for="(project, index) in projects.slice(1)"
          :key="`project-${index + 1}`"
          :name="project.name"
          :category="project.category"
          :image-url="project.imageUrl"
          :url="project.url"
          variant="small"
          :gradient="project.gradient"
          :glass-effect="project.glassEffect"
          :overlay-color="project.overlayColor"
          :glass-bg-color="project.glassBgColor"
          @view-more="handleProjectClick"
        />
      </div>
    </div>

    <!-- Mobile Layout -->
    <div class="block md:hidden">
      <div class="grid grid-cols-1 gap-4">
        <ProjectCard
          v-for="(project, index) in projects"
          :key="`project-mobile-${index}`"
          :name="project.name"
          :category="project.category"
          :image-url="project.imageUrl"
          :url="project.url"
          variant="small"
          :gradient="project.gradient"
          :glass-effect="project.glassEffect"
          :overlay-color="project.overlayColor"
          :glass-bg-color="project.glassBgColor"
          @view-more="handleProjectClick"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { useAnimations } from '~/composables/useAnimations';
import ProjectCard from '../molecules/ProjectCard.vue';

interface Project {
  name: string;
  category: string;
  imageUrl: string;
  url: string;
  gradient?: string;
  glassEffect?: boolean;
  overlayColor?: string; // For simple color overlays like rgba(93, 53, 221, 0.5)
  glassBgColor?: string; // For customizing the glass effect's background tint
}

defineProps<{
  projects: Project[];
}>();

const projectsContainer = ref<HTMLElement | null>(null);
const { animateProjectCards } = useAnimations();

const handleProjectClick = (url: string) => {
  // Handle project click analytics or other logic here
  console.log('Project clicked:', url);
};

onMounted(() => {
  if (projectsContainer.value) {
    animateProjectCards(projectsContainer.value, {
      duration: 0.8,
      stagger: 0.2,
      ease: 'power2.out',
      start: 'top 80%'
    });
  }
});
</script>
