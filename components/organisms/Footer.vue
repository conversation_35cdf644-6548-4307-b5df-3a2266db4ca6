<template>
  <footer id="footer" class="bg-neutral-1 text-neutral-7 py-10 md:py-16 lg:py-20">
    <div class="container mx-auto px-4 md:px-6 lg:px-8">
      <hr class="border-neutral-5 mb-12 md:mb-16 lg:mb-20" />
      <div class="flex flex-col items-end gap-12 md:gap-14 lg:gap-16">
        <div class="w-full flex flex-col items-start md:items-end">
          <h2 class="h2 md:h2-bold lg:h1 mb-6 md:mb-8 lg:mb-10 text-left md:text-right">Get in touch</h2>
          <p class="body-1-regular md:body-1-bold lg:h5-regular text-left md:text-right">
            <a :href="`mailto:${contactEmail}`" class="hover:text-primary-3 transition-colors duration-200">{{ contactEmail }}</a>
          </p>
        </div>

        <div class="w-full flex flex-col md:flex-row md:justify-end gap-8 md:gap-6 lg:gap-8">
          <div class="flex flex-col items-start md:items-end gap-4 md:gap-5 lg:gap-6 text-left md:text-right md:w-[200px] lg:w-[248px]">
            <p class="body-2-regular md:body-2-bold">{{ location }}</p>
            <p class="body-2-bold"><a :href="`tel:${phoneNumber}`" class="hover:text-primary-3 transition-colors duration-200">{{ phoneNumber }}</a></p>
          </div>
          <div class="flex flex-col items-start md:items-end gap-4 md:gap-5 lg:gap-6 text-left md:text-right md:w-[200px] lg:w-[248px]">
            <p class="body-2-regular md:body-2-bold">Social</p>
            <div class="flex flex-row gap-6 md:gap-8 lg:gap-10">
              <div class="flex flex-col items-start md:items-end gap-3 md:gap-4">
                <SocialLink
                  v-for="item in socialLinksColumn1"
                  :key="item.name"
                  :name="item.name"
                  :url="item.url"
                  @click="handleSocialLinkClick"
                />
              </div>
              <div class="flex flex-col items-start md:items-end gap-3 md:gap-4">
                <SocialLink
                  v-for="item in socialLinksColumn2"
                  :key="item.name"
                  :name="item.name"
                  :url="item.url"
                  @click="handleSocialLinkClick"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </footer>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import SocialLink from '../molecules/SocialLink.vue';

interface SocialLinkType {
  name: string;
  url: string;
}

interface Props {
  contactEmail?: string;
  location?: string;
  phoneNumber?: string;
  socialLinks?: SocialLinkType[];
}

const props = withDefaults(defineProps<Props>(), {
  contactEmail: '<EMAIL>',
  location: 'Ho Chi Minh City, VietNam',
  phoneNumber: '+84 327172497',
  socialLinks: () => [
    { name: 'Linkedin', url: 'https://www.linkedin.com/in/huy-nguyen-8aa971174?utm_source=share&utm_campaign=share_via&utm_content=profile&utm_medium=ios_app' },
    { name: 'Whatsapp', url: 'https://wa.me/84327172497' },
  ],
});

const socialLinksColumn1 = computed(() =>
  props.socialLinks.filter((_, index) => index % 2 === 0)
);
const socialLinksColumn2 = computed(() =>
  props.socialLinks.filter((_, index) => index % 2 !== 0)
);

const handleSocialLinkClick = (link: { name: string; url: string }) => {
  // Handle social link click analytics or other logic here
  console.log('Social link clicked:', link);
};
</script>

