<template>
  <section ref="mastheadSection" class="bg-gradient-radial-hero pt-8 sm:pt-6 md:pt-10 lg:pt-[32px] pb-12 sm:pb-10 md:pb-16 lg:pb-[86px] px-4 sm:px-6 md:px-10 lg:px-[120px] rounded-bl-[32px] rounded-br-[32px]">
    <div class="container mx-auto pt-24 sm:pt-20 md:pt-22 lg:pt-24">
      <!-- Hero Content -->
      <div class="flex flex-col lg:flex-row items-center justify-between gap-8 sm:gap-6 md:gap-5 lg:gap-4">
        <!-- Left Content -->
        <div class="text-center lg:text-left lg:w-[513px] sm:w-full">
          <p ref="greeting" class="h5-semibold text-neutral-5 mb-0 md:mb-1 lg:mb-0 md:pl-1 lg:pl-1.5 sm:text-[1.25rem] md:text-[1.375rem] lg:text-[1.5rem]">Hello, I'm <PERSON><PERSON></p>
          <h1 ref="title" class="h1 text-neutral-7 leading-none mt-0 mb-6 md:mb-7 lg:mb-8 sm:text-[3.5rem] md:text-[4rem] lg:text-[6rem]">Fullstack Developer</h1>
          <div ref="buttons" class="flex flex-col sm:flex-row justify-center lg:justify-start gap-3 md:gap-4 lg:gap-[18px]">
            <a href="#footer" @click.prevent="scrollToElement('footer')" class="bg-neutral-7 text-primary-2 rounded-[23px] px-5 md:px-6 lg:px-5 py-3 md:py-3.5 lg:py-3 font-manrope font-semibold text-body-2 md:text-body-1 lg:text-body-2 leading-[1.366] md:leading-[1.4] lg:leading-[1.366] min-h-[44px] md:min-h-[48px] lg:min-h-[44px]">
              Contact me
            </a>
            <a href="#experience" @click.prevent="scrollToElement('experience')" class="bg-transparent border border-neutral-3 text-neutral-7 rounded-[23px] px-5 md:px-6 lg:px-5 py-3 md:py-3.5 lg:py-3 font-manrope font-semibold text-body-2 md:text-body-1 lg:text-body-2 leading-[1.366] md:leading-[1.4] lg:leading-[1.366] bg-gradient-to-b from-white/0 to-white/25 min-h-[44px] md:min-h-[48px] lg:min-h-[44px]">
              See more experience
            </a>
          </div>
        </div>

        <!-- Right Content - Image -->
        <div ref="heroImageContainer" class="sm:w-[300px] md:w-[350px] lg:w-[512px] flex justify-center lg:justify-end">
          <div class="w-[244px] h-[262px] sm:w-[280px] sm:h-[300px] md:w-[320px] md:h-[340px] lg:w-[371px] lg:h-[393px] bg-neutral-7 rounded-[20px] overflow-hidden">
            <img src="~/assets/images/hero-image.png" alt="Huy Nguyen" class="w-full h-full object-cover" />
          </div>
        </div>
      </div>

      <!-- Stats Section -->
      <div ref="statsSection" class="mt-12 sm:mt-16 md:mt-18 lg:mt-20 flex flex-col sm:flex-row items-center justify-center lg:justify-between text-center sm:text-left gap-8 sm:gap-10 md:gap-12 lg:gap-20 flex-wrap">
        <div class="flex items-center gap-3 md:gap-4 w-full sm:w-auto">
          <p class="h2 text-neutral-7 leading-none sm:text-[2.5rem] md:text-[2.75rem] lg:text-[3.5rem]">6+</p>
          <p class="body-1-semibold text-neutral-7 leading-tight sm:text-[1rem] md:text-[1.125rem] lg:text-[1.25rem]">years of <br class="hidden sm:inline"/>experience</p>
        </div>
        <div class="flex items-center gap-3 md:gap-4 w-full sm:w-auto">
          <p class="h2 text-neutral-7 leading-none sm:text-[2.5rem] md:text-[2.75rem] lg:text-[3.5rem]">24</p>
          <p class="body-1-semibold text-neutral-7 leading-tight sm:text-[1rem] md:text-[1.125rem] lg:text-[1.25rem]">typical project on web <br class="hidden sm:inline"/>app system development</p>
        </div>
        <div class="flex items-center gap-3 md:gap-4 w-full sm:w-auto">
          <p class="h2 text-neutral-7 leading-none sm:text-[2.5rem] md:text-[2.75rem] lg:text-[3.5rem]">2+</p>
          <p class="body-1-semibold text-neutral-7 leading-tight sm:text-[1rem] md:text-[1.125rem] lg:text-[1.25rem]">years as <br class="hidden sm:inline"/>Technical Lead</p>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup lang="ts">
import gsap from 'gsap';
import { ref, onMounted } from 'vue';
import { useAnimations } from '~/composables/useAnimations';

const mastheadSection = ref<HTMLElement | null>(null);
const greeting = ref<HTMLElement | null>(null);
const title = ref<HTMLElement | null>(null);
const buttons = ref<HTMLElement | null>(null);
const heroImageContainer = ref<HTMLElement | null>(null);
const statsSection = ref<HTMLElement | null>(null);

// Use animations composable
const { createStarParticles } = useAnimations();

// For hover effect
let mouseMoveTweenX: any = null;
let mouseMoveTweenY: any = null;

// Function to handle smooth scrolling
const scrollToElement = (id: string) => {
  const element = document.getElementById(id);
  if (element) {
    element.scrollIntoView({ behavior: 'smooth' });
  }
};

onMounted(() => {
  if (import.meta.client && mastheadSection.value) {
    // Set initial CSS variables
    mastheadSection.value.style.setProperty('--gradient-y-pos', '-20%');
    mastheadSection.value.style.setProperty('--mouse-x-offset', '0px');
    mastheadSection.value.style.setProperty('--mouse-y-offset', '0px');

    const tl = gsap.timeline({ defaults: { ease: 'power3.out' } });

    // Animate background gradient
    if (mastheadSection.value) {
      tl.to(mastheadSection.value, {
        '--gradient-y-pos': '0%', // Animate to the final position
        duration: 1.0,
        ease: 'sine.inOut',
        delay: 0 // Start background animation early
      });
    }

    // Greeting animation - subtle character reveal
    if (greeting.value) {
      const greetingChars = greeting.value.innerText.split('');
      greeting.value.innerHTML = greetingChars.map(char => `<span>${char === ' ' ? '&nbsp;' : char}</span>`).join('');
      tl.from(greeting.value.querySelectorAll('span'), {
        opacity: 0,
        y: 15,
        rotationX: -90,
        transformOrigin: '0% 50% -50',
        duration: 0.4,
        stagger: 0.02,
        ease: 'sine.out'
      }, "<0.4"); // Start 0.4s after background anim
    }

    // Title animation - Ultra Smooth Character Cascade/Wave In
    if (title.value) {
      const originalText = title.value.innerText;
      const titleEl = title.value; // Capture ref value

      // Split text into words, then words into characters for individual animation
      const words = originalText.split(' ');
      titleEl.innerHTML = words.map(word =>
        `<span class="title-word inline-block">${ // Word wrapper
          word.split('').map(char =>
            char === ' ' ? '&nbsp;' : `<span class="title-char inline-block">${char}</span>` // Character wrapper
          ).join('')
        }</span>`
      ).join('&nbsp;'); // Space between words

      const charElements = titleEl.querySelectorAll('.title-char');

      // Set initial state for characters
      gsap.set(charElements, { opacity: 0, y: 20, filter: 'blur(3px)' });

      tl.to(charElements, {
        opacity: 1,
        y: 0,
        filter: 'blur(0px)',
        duration: 0.6, // Duration for each character's individual animation
        ease: 'expo.out', // A very smooth easing function
        stagger: {
          each: 0.05, // Time between each character starting its animation
          from: "start"
        }
      }, "-=0.2"); // Overlap slightly with greeting animation's end
    }

    // Buttons animation - more dynamic entrance
    if (buttons.value) {
      Array.from(buttons.value.children).forEach((button, index) => {
        tl.from(button, {
          opacity: 0,
          y: 30,
          scale: 0.7,
          rotationX: -45,
          transformOrigin: "center bottom",
          duration: 0.6,
          ease: 'back.out(1.2)',
        }, `-=${index === 0 ? 0.6 : 0.65}`); // Stagger and overlap more with title completion

        // Button hover/active effects (simple scale for now)
        button.addEventListener('mouseenter', () => gsap.to(button, { scale: 1.08, duration: 0.2, ease: 'power2.out' }));
        button.addEventListener('mouseleave', () => gsap.to(button, { scale: 1, duration: 0.2, ease: 'power2.out' }));
        button.addEventListener('mousedown', () => gsap.to(button, { scale: 0.95, duration: 0.1, ease: 'power2.out' }));
        button.addEventListener('mouseup', () => gsap.to(button, { scale: 1.08, duration: 0.1, ease: 'power2.out' }));
      });
    }

    // Hero image entrance animation - more dynamic and impactful
    const heroBaseRotation = -7; // Slightly adjusted
    if (heroImageContainer.value) {
      // Add a "shine" element for hover effect
      const shineElement = document.createElement('div');
      shineElement.style.position = 'absolute';
      shineElement.style.top = '0';
      shineElement.style.left = '0';
      shineElement.style.width = '100%';
      shineElement.style.height = '100%';
      shineElement.style.background = 'linear-gradient(to right, rgba(255,255,255,0) 0%, rgba(255,255,255,0.3) 50%, rgba(255,255,255,0) 100%)';
      shineElement.style.transform = 'translateX(-100%) skewX(-20deg)';
      shineElement.style.opacity = '0.6';
      shineElement.style.pointerEvents = 'none'; // So it doesn't interfere with mouse events on the image
      heroImageContainer.value.style.position = 'relative'; // Needed for absolute positioning of shine
      heroImageContainer.value.style.overflow = 'hidden'; // Keep shine contained
      heroImageContainer.value.appendChild(shineElement);


      tl.fromTo(heroImageContainer.value,
        { opacity: 0, scale: 0.6, rotation: 30, yPercent: 15, filter: 'blur(8px)' },
        {
          opacity: 1,
          scale: 1,
          rotation: heroBaseRotation,
          yPercent: 0,
          filter: 'blur(0px)',
          duration: 1.0,
          ease: 'expo.out'
        },
        "-=0.5" // Overlap with buttons
      );

      // Continuous floating and subtle 3D tilt for the image container
      gsap.to(heroImageContainer.value, {
        yPercent: '-=2',
        xPercent: '+=1',
        rotationZ: '+=0.5', // Keep the base rotation in mind for parallax
        yoyo: true,
        repeat: -1,
        duration: 4,
        ease: 'sine.inOut',
        delay: tl.duration() + 0.5 // Start after main timeline + a small delay
      });
    }

    // Enhanced Stats Section Animation - more dynamic and staggered
    if (statsSection.value) {
      const statItems = Array.from(statsSection.value.children) as HTMLElement[];
      // The stats block animation starts 0.5s before the hero image entrance animation completes.
      // This is equivalent to starting 0.5s after the hero image entrance animation begins (since duration is 1s).
      const statsBlockOverallStartTime = "<+=0.5"; // Relative to the start of the hero image entrance animation

      statItems.forEach((item) => {
        const numberEl = item.querySelector('p.h2') as HTMLElement;
        const textEl = item.querySelector('p.body-1-semibold') as HTMLElement;

        // All stat items will start their animation at the same time.
        const itemCalculatedPosition = statsBlockOverallStartTime;

        if (numberEl) {
          const originalText = numberEl.innerText;
          const hasPlus = originalText.includes('+');
          const targetNumber = parseInt(originalText.replace(/[^\d]/g, '')); // Extract numbers

          if (!isNaN(targetNumber)) {
            let counter = { value: 0 };
            numberEl.innerText = '0' + (hasPlus ? '+' : ''); // Set initial text to 0 or 0+

            tl.to(counter, {
              value: targetNumber,
              duration: 0.9, // Duration of the count-up
              ease: 'sine.out',
              onUpdate: () => {
                numberEl.innerText = Math.round(counter.value) + (hasPlus ? '+' : '');
              }
            }, itemCalculatedPosition);
            // Fade in the number element itself as it counts
            tl.from(numberEl, { opacity: 0, y: 18, duration: 0.5, ease: 'sine.out' }, "<");
          } else {
            // Fallback for non-numeric content if any (should not happen here)
            tl.from(numberEl, { opacity: 0, y: 18, duration: 0.5, ease: 'sine.out' }, itemCalculatedPosition);
          }
        }
        if (textEl) {
          // Text fades in slightly after its corresponding number animation starts
          tl.from(textEl, { opacity: 0, y: 18, duration: 0.5, ease: 'sine.out' }, "<0.2");
        }
      });
    }

    // Mouse move effects
    const section = mastheadSection.value;
    if (section) {
      section.addEventListener('mousemove', (e: MouseEvent) => {
        const rect = section.getBoundingClientRect();
        const x = e.clientX - rect.left;
        const y = e.clientY - rect.top;
        const centerX = rect.width / 2;
        const centerY = rect.height / 2;

        // Background gradient parallax
        const bgOffsetX = (x - centerX) * 0.05;
        const bgOffsetY = (y - centerY) * 0.05;
        if (mouseMoveTweenX) mouseMoveTweenX.kill();
        if (mouseMoveTweenY) mouseMoveTweenY.kill();
        mouseMoveTweenX = gsap.to(section, { '--mouse-x-offset': `${bgOffsetX}px`, duration: 0.3, ease: 'power2.out' });
        mouseMoveTweenY = gsap.to(section, { '--mouse-y-offset': `${bgOffsetY}px`, duration: 0.3, ease: 'power2.out' });

        // Hero image parallax with 3D tilt and shine effect
        if (heroImageContainer.value) {
          const container = heroImageContainer.value;
          const shineElement = container.querySelector('div[style*="translateX"]'); // Find the shine element

          const parallaxStrength = 0.035; // Increased strength
          const tiltStrength = 8; // Max tilt in degrees

          const parallaxOffsetX = (x - centerX) * -parallaxStrength;
          const parallaxOffsetY = (y - centerY) * -parallaxStrength;

          // Calculate tilt based on mouse position relative to center
          const tiltX = ((y - centerY) / centerY) * -tiltStrength; // Tilt up/down
          const tiltY = ((x - centerX) / centerX) * tiltStrength;  // Tilt left/right

          gsap.to(container, {
            x: parallaxOffsetX,
            y: parallaxOffsetY,
            rotationZ: heroBaseRotation + tiltY * 0.2, // Add some Z rotation based on Y tilt
            rotationX: tiltX,
            rotationY: tiltY,
            transformPerspective: 1000, // Important for 3D effect
            duration: 0.7,
            ease: 'power3.out'
          });

          // Animate shine element on hover (if it exists)
          if (shineElement) {
             gsap.killTweensOf(shineElement); // Kill previous shine animation
             gsap.fromTo(shineElement,
                { transform: 'translateX(-100%) skewX(-20deg)' },
                { transform: 'translateX(100%) skewX(-20deg)', duration: 0.7, ease: 'power2.out'}
             );
          }
        }
      });

      section.addEventListener('mouseleave', () => {
        // Reset background gradient parallax
        if (mouseMoveTweenX) mouseMoveTweenX.kill();
        if (mouseMoveTweenY) mouseMoveTweenY.kill();
        mouseMoveTweenX = gsap.to(section, { '--mouse-x-offset': '0px', duration: 0.5, ease: 'power2.out' });
        mouseMoveTweenY = gsap.to(section, { '--mouse-y-offset': '0px', duration: 0.5, ease: 'power2.out' });

        // Reset hero image parallax and tilt
        if (heroImageContainer.value) {
          gsap.to(heroImageContainer.value, {
            x: 0,
            y: 0,
            rotationZ: heroBaseRotation,
            rotationX: 0,
            rotationY: 0,
            duration: 0.7,
            ease: 'power3.out'
          });
        }
      });
    }
     // Add some subtle "star" particles to the background
    createStarParticles(mastheadSection.value, 30);
  }
});


</script>

<style scoped>
.font-manrope {
  font-family: 'Manrope', sans-serif;
}

.bg-gradient-radial-hero {
  /* Use CSS variables for animatable gradient position and hover effect */
  background-image: radial-gradient(
    circle at
    calc(50% + var(--mouse-x-offset, 0px))
    calc(var(--gradient-y-pos, 0%) + var(--mouse-y-offset, 0px)),
    rgba(2, 3, 21, 0) 46.78%,
    rgba(93, 53, 221, 0.7) 72.55%,
    #5D35DD 87.11%,
    #AF87FF 100%
  );
  transition: background-position 0.1s ease-out; /* Add a little native transition for fallback or if JS is slow */
  position: relative; /* Needed for absolute positioning of stars */
  overflow: hidden; /* Keep stars contained if they go off edge */
}

.star-particle { /* Style for star particles if needed, mostly handled by JS */
  pointer-events: none; /* So they don't interfere with mouse events */
}


/* Mobile specific text sizes from Figma */
@media (max-width: 767px) {
  .h1 { /* Fullstack Developer */
    font-size: 3.5rem; /* 56px */
  }
  .h5-semibold { /* Hello, I'm Huy Nguyen */
    font-size: 1.25rem; /* 20px */
  }
  .text-body-2 { /* Button text */
    font-size: 0.875rem; /* 14px */
  }
  .h2 { /* Stats numbers: 6+, 24, 2+ */
    font-size: 2rem; /* 32px */
  }
  .body-1-semibold { /* Stats text */
    font-size: 0.875rem; /* 14px */
  }
}
</style>
