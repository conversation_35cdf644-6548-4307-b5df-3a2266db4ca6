<template>
  <section class="py-16 md:py-20 lg:py-24">
    <div>
      <div class="flex flex-col items-center text-center">
        <h2 class="h5-bold md:h4-bold lg:h3 text-[var(--color-neutral-7)] mb-8 md:mb-10 lg:mb-12">
          {{ props.title || 'Highlighted Technical Skills' }}
        </h2>

        <div class="flex flex-wrap justify-center gap-3 md:gap-4 lg:gap-4 mb-12 md:mb-14 lg:mb-16">
          <CategoryButton
            v-for="cat in categories"
            :key="cat"
            :text="cat"
            :is-selected="selectedCategory === cat"
            :disabled="isAnimating"
            @click="handleCategoryButtonClick"
            class="md:text-base lg:text-base"
          />
        </div>

        <div
          class="grid grid-cols-3 gap-x-2 gap-y-3 md:grid-cols-4 lg:grid-cols-6 md:gap-x-4 lg:gap-x-4 md:gap-y-5 lg:gap-y-6 w-full max-w-xs md:max-w-2xl lg:max-w-4xl"
          ref="skillGridRef"
        >
          <SkillCard
            v-for="skill in filteredSkills"
            :key="`${selectedCategory}-${skill.id}`"
            :name="skill.name"
            :image-url="`/images/skills/${skill.image}`"
          />
        </div>
      </div>
    </div>
  </section>
</template>

<script setup lang="ts">
import { ref, computed, nextTick, onMounted, onBeforeUnmount, watch, defineProps } from 'vue';
import { useAnimations } from '~/composables/useAnimations';
import SkillCard from '../molecules/SkillCard.vue';
import CategoryButton from '../molecules/CategoryButton.vue';

// Define props interface
interface Props {
  title?: string;
  expertiseGroups?: any[]; // We're not using this prop currently but keeping it for compatibility
}

const props = defineProps<Props>();

interface Skill {
  id: number;
  name: string;
  image: string;
}

const categories = ['Frontend Technologies', 'Backend Technologies', 'Databases', 'Cloud & DevOps', 'Development Tools', 'CMS Platforms'] as const;
type Category = typeof categories[number];

const skillGridRef = ref<HTMLElement | null>(null);
const selectedCategory = ref<Category>(categories[0]);
const isAnimating = ref(false);

// Use the animations composable
const { animateSkillsIn, animateSkillsOut, cleanupScrollTriggers } = useAnimations();

const skillsByCategory: Record<Category, Skill[]> = {
  'Frontend Technologies': [
    { id: 1, name: 'HTML5', image: 'html5-logo.svg' },
    { id: 2, name: 'CSS3', image: 'css3-logo.svg' },
    { id: 3, name: 'JavaScript', image: 'javascript-logo.svg' },
    { id: 4, name: 'TypeScript', image: 'typescript-logo.svg' },
    { id: 5, name: 'React.js', image: 'reactjs-logo.svg' },
    { id: 6, name: 'Vue.js', image: 'vuejs-logo.svg' },
    { id: 7, name: 'Next.js', image: 'nextjs-logo.svg' },
    { id: 8, name: 'Nuxt.js', image: 'nuxtjs-logo.svg' },
    { id: 9, name: 'Remix.js', image: 'remixjs-logo.svg' },
    { id: 10, name: 'Gatsby.js', image: 'gatsbyjs-logo.svg' },
    { id: 11, name: 'Svelte', image: 'svelte-logo.svg' },
    { id: 12, name: 'SCSS/Sass', image: 'sass-logo.svg' },
    { id: 13, name: 'Tailwind CSS', image: 'tailwindcss-logo.svg' },
    { id: 14, name: 'Bootstrap', image: 'bootstrap-logo.svg' },
    { id: 15, name: 'jQuery', image: 'jquery-logo.svg' },
  ],
  'Backend Technologies': [
    { id: 16, name: 'Node.js', image: 'nodejs.svg' },
    { id: 17, name: 'NestJS', image: 'nestjs-logo.svg' },
    { id: 18, name: 'Express.js', image: 'expressjs-logo.svg' },
    { id: 19, name: 'PHP', image: 'php-logo.svg' },
    { id: 20, name: 'Laravel', image: 'laravel-logo.svg' },
    { id: 21, name: 'GraphQL', image: 'graphql-logo.svg' },
    { id: 22, name: 'REST API', image: 'rest-api-logo.svg' },
  ],
  'Databases': [
    { id: 23, name: 'MongoDB', image: 'mongodb.svg' },
    { id: 24, name: 'PostgreSQL', image: 'postgresql.svg' },
    { id: 25, name: 'MySQL', image: 'mysql.svg' },
    { id: 26, name: 'Redis', image: 'redis-logo.svg' },
    { id: 50, name: 'Pinecone', image: 'pinecone-logo.svg' },
  ],
  'Cloud & DevOps': [
    { id: 27, name: 'Firebase', image: 'firebase-logo.svg' },
    { id: 28, name: 'AWS', image: 'aws-logo.svg' },
    { id: 29, name: 'Google Cloud', image: 'gcp-logo.svg' },
    { id: 30, name: 'Digital Ocean', image: 'digitalocean-logo.svg' },
    { id: 31, name: 'Docker', image: 'docker.svg' },
    { id: 32, name: 'Vercel', image: 'vercel-logo.svg' },
    { id: 33, name: 'Nginx', image: 'nginx-logo.svg' },
    { id: 34, name: 'Apache', image: 'apache-logo.svg' },
    { id: 52, name: 'TencentCloud', image: 'tencent-cloud-logo.svg' },
    { id: 53, name: 'HuaweiCloud', image: 'huawei-cloud-logo.svg' },
  ],
  'Development Tools': [
    { id: 35, name: 'Git', image: 'git.svg' },
    { id: 36, name: 'GitHub', image: 'github-logo.svg' },
    { id: 37, name: 'GitLab', image: 'gitlab-logo.svg' },
    { id: 38, name: 'Webpack', image: 'webpack-logo.svg' },
    { id: 39, name: 'Vite', image: 'vite-logo.svg' },
    { id: 40, name: 'Rollup', image: 'rollup-logo.svg' },
    { id: 41, name: 'Figma', image: 'figma.svg' },
    { id: 42, name: 'NPM', image: 'npm-logo.svg' },
    { id: 43, name: 'Yarn', image: 'yarn-logo.svg' },
    { id: 51, name: 'LlamaIndex', image: 'llamaindex-logo.svg' },
  ],
  'CMS Platforms': [
    { id: 44, name: 'WordPress', image: 'wordpress-logo.svg' },
    { id: 45, name: 'Shopify', image: 'shopify-logo.svg' },
    { id: 46, name: 'Strapi', image: 'strapi-logo.svg' },
    { id: 47, name: 'Contentful', image: 'contentful-logo.svg' },
    { id: 48, name: 'Ghost', image: 'ghost-logo.svg' },
    { id: 49, name: 'Directus', image: 'directus-logo.svg' },
  ]
};

const filteredSkills = computed(() => {
  return skillsByCategory[selectedCategory.value] || [];
});

onMounted(async () => {
  await nextTick();
  // Initial animation
  if (skillGridRef.value) {
    const items = skillGridRef.value.querySelectorAll('.skill-item');
    if (items.length > 0) {
      isAnimating.value = true;
      await animateSkillsIn(items);
      isAnimating.value = false;
    }
  }
});

// Store the next category to handle transitions properly
const nextCategory = ref<Category | null>(null);

// Separate function to handle the animation process
const animateCategoryChange = async (newCategory: Category, oldCategory: Category | undefined) => {
  if (!skillGridRef.value) return;
  
  isAnimating.value = true;

  try {
    // Animate out current items if this isn't the initial load
    if (oldCategory !== undefined) {
      const currentItems = skillGridRef.value.querySelectorAll('.skill-item');
      if (currentItems.length > 0) {
        await animateSkillsOut(currentItems);
      }
    }

    // Wait for DOM to update with new skills
    await nextTick();

    // Animate in new items
    const newItems = skillGridRef.value.querySelectorAll('.skill-item');
    if (newItems.length > 0) {
      await animateSkillsIn(newItems);
    }
  } catch (error) {
    // Handle any animation errors gracefully
    console.error('Animation error:', error);
  } finally {
    isAnimating.value = false;
  }
};

watch(selectedCategory, async (newCategory, oldCategory) => {
  if (newCategory === oldCategory) return;
  
  if (isAnimating.value) {
    // If already animating, store the next category and return
    nextCategory.value = newCategory;
    return;
  }
  
  // Start the animation process
  await animateCategoryChange(newCategory, oldCategory);
  
  // Check if another category change was requested during animation
  if (nextCategory.value && nextCategory.value !== selectedCategory.value) {
    const next = nextCategory.value;
    nextCategory.value = null;
    selectedCategory.value = next; // This will trigger the watcher again
  }
});

const handleCategoryButtonClick = (category: string) => {
  const typedCategory = category as Category;
  if (selectedCategory.value !== typedCategory) {
    selectedCategory.value = typedCategory; // This triggers the watcher
  }
};

onBeforeUnmount(() => {
  cleanupScrollTriggers();
});
</script>
