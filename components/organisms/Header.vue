<template>
  <header ref="headerElement" class="flex bg-[var(--color-neutral-1)] justify-center items-center py-3 md:py-4 lg:py-3">
    <!-- Desktop Menu - Only visible on large screens (1024px+) -->
    <nav ref="desktopNav" class="hidden lg:flex bg-[var(--color-neutral-2)] rounded-[23px] px-3 md:px-4 lg:px-3 space-x-4 md:space-x-6 lg:space-x-4">
      <MenuItem
        v-for="(item, index) in menuItems"
        :key="index"
        :text="item.text"
        :link="item.link"
        :selected="item.selected"
        variant="desktop"
        @click="handleMenuItemClick"
        class="lg:py-3"
      />
    </nav>

    <!-- Mobile/Tablet Menu - Visible on mobile and tablets (including iPad mini/air) -->
    <div class="lg:hidden flex w-full justify-between items-center">
      <div class="relative">
        <button @click="toggleMobileMenu" class="bg-[var(--color-neutral-2)] rounded-[23px] p-3 flex items-center space-x-2 min-h-[44px]">
          <span class="font-manrope font-bold text-sm leading-[1.4] text-[var(--color-neutral-7)]">{{ selectedMobileItemText }}</span>
          <svg :class="{'rotate-180': mobileMenuOpen}" class="w-[18px] h-[18px] text-[var(--color-neutral-7)] transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M19 9l-7 7-7-7"></path></svg>
        </button>
        <Transition name="dropdown">
          <nav v-if="mobileMenuOpen" class="absolute top-full left-0 mt-2 w-48 bg-[var(--color-neutral-2)] rounded-[23px] p-3 shadow-lg z-50">
            <a
              v-for="(item, index) in menuItems"
              :key="index"
              :href="item.link"
              @click="(event) => selectMobileItem(item, event)"
              class="flex items-center font-manrope font-bold text-sm leading-[1.4] px-3 py-2 md:px-4 md:py-3 rounded-[18px] min-h-[44px] md:min-h-[46px]"
              :class="item.selected ? 'bg-[var(--color-primary-2)] text-[var(--color-neutral-7)]' : 'text-[#595B80] hover:text-[var(--color-neutral-7)]'"
            >
              {{ item.text }}
            </a>
          </nav>
        </Transition>
      </div>
      <a href="#footer" class="bg-[var(--color-primary-2)] rounded-[23px] px-4 py-2.5 font-manrope font-semibold text-sm leading-[1.366] text-[var(--color-neutral-7)] min-h-[44px] flex items-center">
        Contact me
      </a>
    </div>

    <!-- Desktop Contact Button - Only visible on large screens -->
    <a ref="desktopContactBtn" href="#footer" class="hidden lg:flex items-center bg-[var(--color-primary-2)] rounded-[23px] px-5 md:px-6 lg:px-5 py-3 md:py-3.5 lg:py-3 ml-3 md:ml-4 lg:ml-3 font-manrope font-semibold text-body-2 md:text-body-2 lg:text-body-2 leading-[1.366] text-[var(--color-neutral-7)] min-h-[44px] md:min-h-[46px] lg:min-h-[44px]">
      Contact me
    </a>
  </header>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onBeforeUnmount } from 'vue';
import { useAnimations } from '~/composables/useAnimations';
import MenuItem from '../molecules/MenuItem.vue';

interface MenuItemType {
  text: string;
  link: string;
  selected?: boolean;
}

const props = defineProps<{
  menuItems: MenuItemType[];
}>();

const mobileMenuOpen = ref(false);
const internalMenuItems = ref<MenuItemType[]>(props.menuItems.map(item => ({ ...item })));

const toggleMobileMenu = () => {
  mobileMenuOpen.value = !mobileMenuOpen.value;
  
  // Add click outside listener when menu is opened
  if (mobileMenuOpen.value) {
    setTimeout(() => {
      window.addEventListener('click', handleClickOutside);
    }, 0);
  } else {
    window.removeEventListener('click', handleClickOutside);
  }
};

const handleClickOutside = (event: MouseEvent) => {
  const target = event.target as HTMLElement;
  const mobileMenuContainer = document.querySelector('.relative');
  
  if (mobileMenuContainer && !mobileMenuContainer.contains(target)) {
    mobileMenuOpen.value = false;
    window.removeEventListener('click', handleClickOutside);
  }
};

const selectMobileItem = (selectedItem: MenuItemType, event?: Event) => {
  // Update selected state
  internalMenuItems.value.forEach((item: MenuItemType) => {
    item.selected = item.text === selectedItem.text;
  });
  
  // Close mobile menu
  mobileMenuOpen.value = false;
  
  // Handle anchor link scrolling
  if (selectedItem.link.startsWith('#') && event) {
    event.preventDefault();
    const targetId = selectedItem.link.substring(1);
    const targetElement = document.getElementById(targetId);
    
    if (targetElement) {
      // Smooth scroll to the target element
      targetElement.scrollIntoView({ behavior: 'smooth' });
    }
  }
};

const selectedMobileItemText = computed(() => {
  const selectedItem = internalMenuItems.value.find((item: MenuItemType) => item.selected);
  return selectedItem ? selectedItem.text : (internalMenuItems.value.length > 0 ? internalMenuItems.value[0].text : 'Menu');
});

const handleMenuItemClick = (item: { text: string; link: string; selected?: boolean }) => {
  // Update selected state
  internalMenuItems.value.forEach((menuItem: MenuItemType) => {
    menuItem.selected = menuItem.text === item.text;
  });
  
  // Handle anchor link scrolling
  if (item.link.startsWith('#')) {
    const targetId = item.link.substring(1);
    const targetElement = document.getElementById(targetId);
    
    if (targetElement) {
      // Close mobile menu if open
      mobileMenuOpen.value = false;
      
      // Smooth scroll to the target element
      targetElement.scrollIntoView({ behavior: 'smooth' });
    }
  }
};

// Ensure at least one item is selected by default for mobile, or the first one
if (internalMenuItems.value.length > 0 && !internalMenuItems.value.some((item: MenuItemType) => item.selected)) {
  internalMenuItems.value[0].selected = true;
}

const headerElement = ref<HTMLElement | null>(null);
const desktopNav = ref<HTMLElement | null>(null);
const desktopContactBtn = ref<HTMLElement | null>(null);
// For mobile, we might animate the whole mobile menu container or individual items when opened.
// For simplicity, let's animate the main header elements on load.

onMounted(() => {
  if (import.meta.client && headerElement.value) {
    const { animateHeaderIn } = useAnimations();

    const mobileHeaderElements = headerElement.value.querySelectorAll('.md\\:hidden > div, .md\\:hidden > a');

    animateHeaderIn(
      desktopNav.value,
      desktopContactBtn.value,
      mobileHeaderElements,
      {
        duration: 0.5,
        stagger: 0.1,
        delay: 0.3
      }
    );
  }
});

onBeforeUnmount(() => {
  // Clean up event listeners when component is unmounted
  window.removeEventListener('click', handleClickOutside);
});
</script>

<style scoped>
/* Manrope font needs to be available globally or imported here */
.font-manrope {
  font-family: 'Manrope', sans-serif;
}

/* Dropdown animation */
.dropdown-enter-active,
.dropdown-leave-active {
  transition: opacity 0.3s ease, transform 0.3s ease;
}

.dropdown-enter-from,
.dropdown-leave-to {
  opacity: 0;
  transform: translateY(-10px);
}
</style>
