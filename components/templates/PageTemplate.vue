<template>
  <!-- Header Slot with Default -->
  <slot name="header">
    <Header :menu-items="defaultMenuItems" class="mx-auto px-4 md:px-6 lg:px-8 mb-3 md:mb-4 lg:mb-5 w-full fixed top-0 z-50" />
  </slot>

  <div class="min-h-screen flex flex-col">
    <!-- Main content -->
    <main class="flex-grow">
      <div :class="containerClass">
        <slot></slot>
      </div>
    </main>
  </div>

  <!-- Footer Slot with Default -->
  <slot name="footer">
    <Footer />
  </slot>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { useHead } from '#app'; // Or 'nuxt/app'
import Footer from '~/components/organisms/Footer.vue';
import Header from "~/components/organisms/Header.vue";

// Define props
const props = defineProps<{
  title?: string;
  containerClass?: string;
}>();

// Computed container class with default
const containerClass = computed(() =>
  props.containerClass || 'container mx-auto px-4'
);

// Set page title if provided
if (props.title) {
  useHead({
    title: props.title,
  });
}

interface MenuItem {
  text: string;
  link: string;
  selected?: boolean;
}

// Default menu items for the masthead, can be customized via props if needed in the future
const defaultMenuItems: MenuItem[] = [
  { text: 'About me', link: '#about', selected: true },
  { text: 'Skills', link: '#skills' },
  { text: 'Projects', link: '#projects' },
  { text: 'Experience', link: '#experience' },
  { text: 'Education', link: '#education' },
];

</script>
