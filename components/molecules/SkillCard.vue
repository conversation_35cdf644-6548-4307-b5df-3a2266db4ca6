<template>
  <div
    ref="skillCardRef"
    class="skill-item bg-white flex items-center justify-center h-[50px] md:h-[70px] lg:h-[80px] p-3 md:p-3.5 lg:p-3 rounded-lg shadow hover:shadow-lg transition-all duration-300 ease-out transform-gpu hover:scale-105 hover:-translate-y-1"
  >
    <img
      :src="imageUrl"
      :alt="name"
      :title="name"
      :class="imageClasses"
      :loading="lazy ? 'lazy' : 'eager'"
      v-show="loaded"
      @load="onImageLoaded"
    />
    <span class="sr-only">{{ name }}</span>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, onMounted, onBeforeUnmount } from 'vue';
import { useAnimations } from '~/composables/useAnimations';

interface Props {
  name: string;
  imageUrl: string;
  lazy?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  lazy: true
});

const loaded = ref(false);
const skillCardRef = ref<HTMLElement | null>(null);
const { fadeInOnScroll } = useAnimations();

// Pre-load the image to avoid flashing when changing categories
onMounted(() => {
  // If the image is already in browser cache, it might be loaded immediately
  const img = new Image();
  img.onload = () => {
    loaded.value = true;
  };
  img.onerror = () => {
    // Still set loaded to true even on error to avoid blocking the UI
    loaded.value = true;
    console.error(`Failed to load image: ${props.imageUrl}`);
  };

  // Set src after adding event listeners
  img.src = props.imageUrl;

  // Check if already complete (might be cached)
  if (img.complete) {
    loaded.value = true;
  }

  // Add scroll animation for individual skill cards
  if (skillCardRef.value) {
    fadeInOnScroll(skillCardRef.value, {
      duration: 0.6,
      ease: 'back.out(1.2)',
      start: 'top 90%',
      end: 'bottom 10%'
    });
  }
});

const onImageLoaded = () => {
  loaded.value = true;
};

const imageClasses = computed(() => {
  // Simplified classes for better performance and cleaner appearance
  return 'max-h-[40px] md:max-h-[55px] lg:max-h-[60px] max-w-[80%] md:max-w-[80%] lg:max-w-[80%] object-contain';
});
</script>
