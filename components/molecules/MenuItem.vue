<template>
  <a
    ref="menuItemRef"
    :href="link"
    class="font-manrope block font-bold text-body-2 md:text-body-3 lg:text-body-2 leading-[1.4] p-3 md:py-2.5 md:px-3.5 lg:p-3 rounded-[18px] transition-all duration-300 ease-out transform-gpu"
    :class="[
      selected ? 'text-neutral-7 bg-white/10' : 'text-[#595B80] hover:text-neutral-7 hover:bg-white/5',
      variant === 'mobile' ? 'w-full text-left' : '',
      'hover:scale-105 active:scale-95'
    ]"
    @click="handleClick"
  >
    <span class="relative z-10">
      {{ text }}
    </span>
  </a>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount } from 'vue';
import { useAnimations } from '~/composables/useAnimations';

interface Props {
  text: string;
  link: string;
  selected?: boolean;
  variant?: 'desktop' | 'mobile';
  enableHoverAnimation?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  selected: false,
  variant: 'desktop',
  enableHoverAnimation: true
});

const emit = defineEmits<{
  click: [item: { text: string; link: string; selected?: boolean }];
}>();

const menuItemRef = ref<HTMLElement | null>(null);
const { animateMenuItemHover } = useAnimations();
let cleanupHoverAnimation: (() => void) | null = null;

const handleClick = (event: Event) => {
  // Emit the click event with item data
  emit('click', {
    text: props.text,
    link: props.link,
    selected: props.selected
  });

  // If it's an anchor link, prevent default behavior
  // The parent component will handle the smooth scrolling
  if (props.link.startsWith('#')) {
    event.preventDefault();
  }
};

onMounted(() => {
  if (menuItemRef.value && props.enableHoverAnimation) {
    cleanupHoverAnimation = animateMenuItemHover(menuItemRef.value, {
      scale: 1.02,
      duration: 0.2,
      ease: 'power2.out'
    });
  }
});

onBeforeUnmount(() => {
  if (cleanupHoverAnimation) {
    cleanupHoverAnimation();
  }
});
</script>
