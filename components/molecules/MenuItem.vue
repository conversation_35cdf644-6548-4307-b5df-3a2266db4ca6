<template>
  <a
    :href="link"
    class="font-manrope block font-bold text-body-2 md:text-body-3 lg:text-body-2 leading-[1.4] p-3 md:py-2.5 md:px-3.5 lg:p-3 rounded-[18px] transition-colors duration-200"
    :class="[
      selected ? 'text-neutral-7' : 'text-[#595B80] hover:text-neutral-7',
      variant === 'mobile' ? 'w-full text-left' : ''
    ]"
    @click="handleClick"
  >
    {{ text }}
  </a>
</template>

<script setup lang="ts">
interface Props {
  text: string;
  link: string;
  selected?: boolean;
  variant?: 'desktop' | 'mobile';
}

const props = withDefaults(defineProps<Props>(), {
  selected: false,
  variant: 'desktop'
});

const emit = defineEmits<{
  click: [item: { text: string; link: string; selected?: boolean }];
}>();

const handleClick = (event: Event) => {
  // Emit the click event with item data
  emit('click', {
    text: props.text,
    link: props.link,
    selected: props.selected
  });
  
  // If it's an anchor link, prevent default behavior
  // The parent component will handle the smooth scrolling
  if (props.link.startsWith('#')) {
    event.preventDefault();
  }
};
</script>
