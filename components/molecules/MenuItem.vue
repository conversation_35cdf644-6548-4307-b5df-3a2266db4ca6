<template>
  <a
    ref="menuItemRef"
    :href="link"
    class="font-manrope block font-bold text-body-2 md:text-body-3 lg:text-body-2 leading-[1.4] p-3 md:py-2.5 md:px-3.5 lg:p-3 rounded-[18px] transition-all duration-300 ease-out transform-gpu"
    :class="[
      selected ? 'text-[var(--color-neutral-7)]' : 'text-[#595B80] hover:text-[var(--color-neutral-7)]',
      variant === 'mobile' ? 'w-full text-left' : '',
      'hover:scale-[1.02] active:scale-95'
    ]"
    @click="handleClick"
  >
    <span class="relative z-10">
      {{ text }}
    </span>
  </a>
</template>

<script setup lang="ts">
// Removed GSAP imports since we're using CSS transitions for better performance

interface Props {
  text: string;
  link: string;
  selected?: boolean;
  variant?: 'desktop' | 'mobile';
}

const props = withDefaults(defineProps<Props>(), {
  selected: false,
  variant: 'desktop'
});

const emit = defineEmits<{
  click: (item: { text: string; link: string; selected?: boolean }) => void;
}>();

const handleClick = (event: Event) => {
  // Emit the click event with item data
  emit('click', {
    text: props.text,
    link: props.link,
    selected: props.selected
  });

  // If it's an anchor link, prevent default behavior
  // The parent component will handle the smooth scrolling
  if (props.link.startsWith('#')) {
    event.preventDefault();
  }
};
</script>
