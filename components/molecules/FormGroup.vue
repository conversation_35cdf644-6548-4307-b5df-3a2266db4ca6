<template>
  <div class="space-y-4 md:space-y-5 lg:space-y-4">
    <Input
      v-model="inputValue"
      :label="label"
      :placeholder="placeholder"
      :type="type"
      :helper-text="helperText"
      :error="error"
      :required="required"
      :disabled="disabled"
    />
    <Button
      v-if="showButton"
      :variant="buttonVariant"
      :disabled="disabled || !inputValue"
      @click="handleSubmit"
      size="md"
      class="min-w-[100px] md:min-w-[120px] lg:min-w-[100px]"
    >
      {{ buttonText }}
    </Button>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';
import Input from '../atoms/Input.vue';
import Button from '../atoms/Button.vue';

const props = defineProps({
  modelValue: {
    type: [String, Number],
    default: ''
  },
  label: {
    type: String,
    default: ''
  },
  placeholder: {
    type: String,
    default: ''
  },
  type: {
    type: String,
    default: 'text'
  },
  helperText: {
    type: String,
    default: ''
  },
  error: {
    type: String,
    default: ''
  },
  required: {
    type: Boolean,
    default: false
  },
  disabled: {
    type: Boolean,
    default: false
  },
  showButton: {
    type: Boolean,
    default: true
  },
  buttonText: {
    type: String,
    default: 'Submit'
  },
  buttonVariant: {
    type: String as () => 'primary' | 'secondary' | 'outline',
    default: 'primary'
  }
});

const emit = defineEmits(['update:modelValue', 'submit']);

const inputValue = ref(props.modelValue);

watch(() => props.modelValue, (newValue) => {
  inputValue.value = newValue;
});

watch(inputValue, (newValue) => {
  emit('update:modelValue', newValue);
});

const handleSubmit = () => {
  emit('submit', inputValue.value);
};
</script>