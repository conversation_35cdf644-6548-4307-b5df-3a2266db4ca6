<template>
  <button
    :class="[
      'py-2.5 px-4 md:py-3 lg:py-3 md:px-5 lg:px-5 rounded-[23px] transition-all duration-300 ease-out min-h-[44px] md:min-h-[46px] lg:min-h-[44px] transform-gpu',
      'body-3-bold md:body-3-bold lg:label-1',
      isSelected
        ? 'bg-[var(--color-primary-2)] text-[var(--color-neutral-7)] shadow-lg scale-105'
        : 'bg-[var(--color-neutral-2)] text-[var(--color-neutral-3)] hover:bg-[var(--color-neutral-3)] hover:text-[var(--color-neutral-7)] hover:scale-105 hover:shadow-md',
      disabled ? 'opacity-60 cursor-not-allowed' : 'cursor-pointer active:scale-95'
    ]"
    @click="handleClick"
    :disabled="disabled"
  >
    {{ text }}
  </button>
</template>

<script setup lang="ts">
interface Props {
  text: string;
  isSelected?: boolean;
  disabled?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  isSelected: false,
  disabled: false
});

const emit = defineEmits<{
  click: [category: string];
}>();

const handleClick = () => {
  if (!props.disabled) {
    emit('click', props.text);
  }
};
</script>
