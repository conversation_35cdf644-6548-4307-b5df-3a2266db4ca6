<template>
  <!-- Large Project Card for Desktop -->
  <div
    v-if="variant === 'large'"
    class="project-card rounded-3xl bg-cover bg-center relative min-h-[400px] md:min-h-[450px] lg:min-h-[400px] overflow-hidden group transition-all duration-500 ease-in-out hover:shadow-2xl transform-gpu"
    :style="backgroundStyle"
  >
    <!-- Glass effect overlay for large card -->
    <div
      class="absolute bottom-8 md:bottom-10 lg:bottom-8 right-8 md:right-10 lg:right-12 w-[80%] md:w-[85%] lg:w-[600px] p-6 md:p-8 rounded-3xl flex flex-col md:flex-row justify-between items-start md:items-end transition-all duration-500 ease-in-out group-hover:bottom-12 md:group-hover:bottom-16 group-hover:shadow-2xl gap-4 md:gap-6 lg:gap-4"
      style="background-color: rgba(255, 255, 255, 0.5); backdrop-filter: blur(6px);"
    >
      <div class="mb-4 md:mb-0">
        <h3 class="h4-bold md:h3-bold lg:h4-bold text-[var(--color-primary-1)]">{{ name }}</h3>
        <p class="body-2-bold md:body-1-bold lg:body-2-bold text-[var(--color-primary-1)]">{{ category }}</p>
      </div>
      <Button
        variant="secondary"
        class="bg-white text-[var(--color-primary-2)] py-3 md:py-4 lg:py-3 px-5 md:px-6 lg:px-5 rounded-full body-2-bold md:body-1-bold lg:body-2-bold hover:bg-[var(--color-neutral-6)] transition-all duration-300 ease-in-out hover:scale-105 hover:shadow-2xl min-h-[44px] md:min-h-[48px] lg:min-h-[44px]"
        @click="handleViewMore"
      >
        View more
      </Button>
    </div>
  </div>

  <!-- Small Project Card for Desktop and Mobile -->
  <div
    v-else
    class="rounded-3xl bg-cover bg-center relative min-h-[360px] md:min-h-[400px] lg:min-h-[360px] overflow-hidden group transition-all duration-300 ease-in-out hover:shadow-xl"
    :style="backgroundStyle"
  >
    <!-- Content overlay for small cards -->
    <div
      class="absolute inset-0 flex flex-col justify-between p-6 md:p-8"
      :style="overlayStyle"
    >
      <!-- Content positioned at bottom -->
      <div class="flex-grow"></div>
      <div class="text-white">
        <h3 class="h4-bold md:h3-bold lg:h4-bold mb-2 md:mb-3 lg:mb-2">{{ name }}</h3>
        <p class="body-2-bold md:body-1-bold lg:body-2-bold mb-4 md:mb-6 lg:mb-4">{{ category }}</p>
        <Button
          variant="secondary"
          class="bg-white text-[var(--color-primary-2)] py-3 md:py-4 lg:py-3 px-5 md:px-6 lg:px-5 rounded-full body-2-bold md:body-1-bold lg:body-2-bold hover:bg-[var(--color-neutral-6)] transition-all duration-300 ease-in-out hover:scale-105 hover:shadow-2xl min-h-[44px] md:min-h-[48px] lg:min-h-[44px]"
          @click="handleViewMore"
        >
          View more
        </Button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import Button from '../atoms/Button.vue';

interface Props {
  name: string;
  category: string;
  imageUrl: string;
  url: string;
  variant?: 'large' | 'small';
  gradient?: string;
  glassEffect?: boolean;
  overlayColor?: string;
  glassBgColor?: string;
}

const props = withDefaults(defineProps<Props>(), {
  variant: 'large',
  glassEffect: false
});

const emit = defineEmits<{
  viewMore: [url: string];
}>();

// Computed style for background image and overlays
const backgroundStyle = computed(() => {
  let style: any = {
    backgroundImage: `url(${props.imageUrl})`
  };

  // For large cards, add overlay color if specified
  if (props.variant === 'large' && props.overlayColor) {
    style.backgroundImage = `linear-gradient(${props.overlayColor}, ${props.overlayColor}), url(${props.imageUrl})`;
  }

  return style;
});

// Computed style for overlay effects on small cards
const overlayStyle = computed(() => {
  if (props.variant === 'small') {
    if (props.gradient) {
      return { background: props.gradient };
    } else if (props.overlayColor) {
      return { backgroundColor: props.overlayColor };
    }
  }
  return {};
});

const handleViewMore = () => {
  emit('viewMore', props.url);
  // Also open the URL for default behavior
  window.open(props.url, '_blank');
};
</script>
