<template>
  <a
    :href="url"
    target="_blank"
    rel="noopener noreferrer"
    class="body-2-bold md:body-1-bold lg:body-2-bold hover:text-primary-3 transition-colors duration-200"
    @click="handleClick"
  >
    {{ name }}
  </a>
</template>

<script setup lang="ts">
interface Props {
  name: string;
  url: string;
}

const props = defineProps<Props>();

const emit = defineEmits<{
  click: [link: { name: string; url: string }];
}>();

const handleClick = () => {
  emit('click', {
    name: props.name,
    url: props.url
  });
};
</script>
