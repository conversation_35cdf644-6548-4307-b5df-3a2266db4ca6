<template>
  <button
    :class="[
      'px-4 md:px-5 lg:px-4 py-2 md:py-3 lg:py-2 rounded-md transition-colors min-h-[44px] md:min-h-[48px] lg:min-h-[44px] flex items-center justify-center',
      variant === 'primary' ? 'bg-blue-500 hover:bg-blue-600 text-white' : '',
      variant === 'secondary' ? 'bg-gray-200 hover:bg-gray-300 text-gray-800' : '',
      variant === 'outline' ? 'border border-blue-500 text-blue-500 hover:bg-blue-50' : '',
      size === 'sm' ? 'text-sm md:text-base lg:text-sm' : '',
      size === 'md' ? 'text-base md:text-lg lg:text-base' : '',
      size === 'lg' ? 'text-lg md:text-xl lg:text-lg px-6 md:px-7 lg:px-6 py-3 md:py-4 lg:py-3' : '',
      fullWidth ? 'w-full' : '',
      disabled ? 'opacity-50 cursor-not-allowed' : ''
    ]"
    :disabled="disabled"
    @click="$emit('click', $event)"
  >
    <slot></slot>
  </button>
</template>

<script setup lang="ts">
defineProps({
  variant: {
    type: String as () => 'primary' | 'secondary' | 'outline',
    default: 'primary'
  },
  size: {
    type: String as () => 'sm' | 'md' | 'lg',
    default: 'md'
  },
  fullWidth: {
    type: Boolean,
    default: false
  },
  disabled: {
    type: Boolean,
    default: false
  }
});

defineEmits(['click']);
</script>