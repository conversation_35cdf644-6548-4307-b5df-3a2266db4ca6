<template>
  <button
    ref="buttonRef"
    :class="[
      'px-4 md:px-5 lg:px-4 py-2 md:py-3 lg:py-2 rounded-md transition-all duration-300 ease-out min-h-[44px] md:min-h-[48px] lg:min-h-[44px] flex items-center justify-center transform-gpu',
      variant === 'primary' ? 'bg-blue-500 hover:bg-blue-600 text-white hover:shadow-lg' : '',
      variant === 'secondary' ? 'bg-gray-200 hover:bg-gray-300 text-gray-800 hover:shadow-md' : '',
      variant === 'outline' ? 'border border-blue-500 text-blue-500 hover:bg-blue-50 hover:shadow-md' : '',
      size === 'sm' ? 'text-sm md:text-base lg:text-sm' : '',
      size === 'md' ? 'text-base md:text-lg lg:text-base' : '',
      size === 'lg' ? 'text-lg md:text-xl lg:text-lg px-6 md:px-7 lg:px-6 py-3 md:py-4 lg:py-3' : '',
      fullWidth ? 'w-full' : '',
      disabled ? 'opacity-50 cursor-not-allowed' : 'hover:scale-105 active:scale-95'
    ]"
    :disabled="disabled"
    @click="$emit('click', $event)"
  >
    <span class="relative z-10">
      <slot></slot>
    </span>
  </button>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount } from 'vue';
import { useAnimations } from '~/composables/useAnimations';

const props = defineProps({
  variant: {
    type: String as () => 'primary' | 'secondary' | 'outline',
    default: 'primary'
  },
  size: {
    type: String as () => 'sm' | 'md' | 'lg',
    default: 'md'
  },
  fullWidth: {
    type: Boolean,
    default: false
  },
  disabled: {
    type: Boolean,
    default: false
  },
  enableHoverAnimation: {
    type: Boolean,
    default: true
  }
});

defineEmits(['click']);

const buttonRef = ref<HTMLElement | null>(null);
const { animateButtonHover } = useAnimations();
let cleanupHoverAnimation: (() => void) | null = null;

onMounted(() => {
  if (buttonRef.value && props.enableHoverAnimation && !props.disabled) {
    cleanupHoverAnimation = animateButtonHover(buttonRef.value, {
      scale: props.size === 'lg' ? 1.03 : 1.05,
      duration: 0.3,
      ease: 'power2.out'
    });
  }
});

onBeforeUnmount(() => {
  if (cleanupHoverAnimation) {
    cleanupHoverAnimation();
  }
});
</script>