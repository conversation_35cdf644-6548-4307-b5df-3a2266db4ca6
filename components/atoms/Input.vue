<template>
  <div class="w-full">
    <label v-if="label" :for="id" class="block text-sm md:text-base lg:text-sm font-medium text-gray-700 mb-1 md:mb-2 lg:mb-1">
      {{ label }}
      <span v-if="required" class="text-red-500">*</span>
    </label>
    <input
      :id="id"
      :type="type"
      :value="modelValue"
      :placeholder="placeholder"
      :disabled="disabled"
      :required="required"
      :class="[
        'w-full px-3 md:px-4 lg:px-3 py-2 md:py-3 lg:py-2 border rounded-md focus:outline-none focus:ring-2 transition-all min-h-[44px] md:min-h-[48px] lg:min-h-[44px] text-base md:text-lg lg:text-base',
        error ? 'border-red-300 focus:ring-red-500' : 'border-gray-300 focus:ring-blue-500',
        disabled ? 'bg-gray-100 cursor-not-allowed' : 'bg-white'
      ]"
      @input="$emit('update:modelValue', ($event.target as HTMLInputElement).value)"
    />
    <p v-if="error" class="mt-1 md:mt-2 lg:mt-1 text-sm md:text-base lg:text-sm text-red-600">{{ error }}</p>
    <p v-else-if="helperText" class="mt-1 md:mt-2 lg:mt-1 text-sm md:text-base lg:text-sm text-gray-500">{{ helperText }}</p>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';

const props = defineProps({
  modelValue: {
    type: [String, Number],
    default: ''
  },
  label: {
    type: String,
    default: ''
  },
  id: {
    type: String,
    default: ''
  },
  type: {
    type: String,
    default: 'text'
  },
  placeholder: {
    type: String,
    default: ''
  },
  helperText: {
    type: String,
    default: ''
  },
  error: {
    type: String,
    default: ''
  },
  disabled: {
    type: Boolean,
    default: false
  },
  required: {
    type: Boolean,
    default: false
  }
});

// Generate a unique ID if none is provided
const inputId = computed(() => props.id || `input-${Math.random().toString(36).substring(2, 9)}`);

defineEmits(['update:modelValue']);
</script>