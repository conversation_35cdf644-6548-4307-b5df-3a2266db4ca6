import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';

gsap.registerPlugin(ScrollTrigger);

export interface AnimationOptions {
  duration?: number;
  delay?: number;
  stagger?: number;
  ease?: string;
}

export interface ScrollTriggerOptions {
  trigger?: string | Element;
  start?: string;
  end?: string;
  scrub?: boolean | number;
  pin?: boolean;
  markers?: boolean;
}

/**
 * Composable for handling GSAP animations throughout the application
 */
export const useAnimations = () => {

  /**
   * Animate elements in with elastic effect (for skills)
   */
  const animateSkillsIn = (
    items: NodeListOf<Element> | Element[],
    options: AnimationOptions = {}
  ): Promise<void> => {
    const {
      duration = 1,
      stagger = 0.1,
      ease = 'elastic.out(1, 0.75)'
    } = options;

    return new Promise((resolve) => {
      if (!items || items.length === 0) {
        resolve();
        return;
      }

      gsap.fromTo(items,
        { opacity: 0, scale: 0.2, y: 75, x: -30, rotation: -15 },
        {
          opacity: 1,
          scale: 1,
          y: 0,
          x: 0,
          rotation: 0,
          duration,
          stagger,
          ease,
          overwrite: 'auto',
          onComplete: resolve,
        }
      );
    });
  };

  /**
   * Animate elements out with smooth transition
   */
  const animateSkillsOut = (
    items: NodeListOf<Element> | Element[],
    options: AnimationOptions = {}
  ): Promise<void> => {
    const {
      duration = 0.4,
      stagger = 0.05,
      ease = 'power2.in'
    } = options;

    return new Promise((resolve) => {
      if (!items || items.length === 0) {
        resolve();
        return;
      }

      gsap.to(items, {
        opacity: 0,
        scale: 0.5,
        y: -30,
        rotation: 10,
        duration,
        stagger,
        ease,
        overwrite: 'auto',
        onComplete: resolve,
      });
    });
  };

  /**
   * Animate header elements on mount
   */
  const animateHeaderIn = (
    navElement: Element | null,
    contactButton: Element | null,
    mobileElements: NodeListOf<Element> | null,
    options: AnimationOptions = {}
  ) => {
    const {
      duration = 0.5,
      stagger = 0.1,
      delay = 0.3,
      ease = 'power3.out'
    } = options;

    if (!navElement && !contactButton && !mobileElements) return;

    const tl = gsap.timeline({ defaults: { ease } });

    // Animate desktop navigation items
    if (navElement && navElement.children) {
      tl.from(navElement.children, {
        opacity: 0,
        y: -20,
        duration,
        stagger,
        delay
      });
    }

    // Animate desktop contact button
    if (contactButton) {
      tl.from(contactButton, {
        opacity: 0,
        y: -20,
        duration
      }, "-=0.3");
    }

    // Animate mobile elements
    if (mobileElements && mobileElements.length > 0) {
      tl.from(mobileElements, {
        opacity: 0,
        y: -20,
        duration,
        stagger,
        delay
      }, 0);
    }
  };

  /**
   * Animate masthead elements
   */
  const animateMastheadIn = (
    greeting: Element | null,
    title: Element | null,
    buttons: Element | null,
    description: Element | null,
    image: Element | null,
    options: AnimationOptions = {}
  ) => {
    const {
      duration = 0.8,
      ease = 'power3.out'
    } = options;

    const tl = gsap.timeline({ defaults: { ease } });

    if (greeting) {
      tl.from(greeting, {
        opacity: 0,
        y: 30,
        duration
      });
    }

    if (title) {
      tl.from(title, {
        opacity: 0,
        y: 50,
        duration
      }, `-=${duration * 0.7}`);
    }

    if (description) {
      tl.from(description, {
        opacity: 0,
        y: 30,
        duration
      }, `-=${duration * 0.5}`);
    }

    if (buttons) {
      tl.from(buttons.children, {
        opacity: 0,
        y: 30,
        duration,
        stagger: 0.1
      }, `-=${duration * 0.3}`);
    }

    if (image) {
      tl.from(image, {
        opacity: 0,
        scale: 0.8,
        duration: duration * 1.2
      }, `-=${duration * 0.8}`);
    }
  };

  /**
   * Create floating star particles
   */
  const createStarParticles = (
    container: HTMLElement,
    count: number = 50
  ) => {
    if (!container) return;

    for (let i = 0; i < count; i++) {
      const star = document.createElement('div');
      star.classList.add('star-particle');

      const size = gsap.utils.random(1, 4);
      star.style.width = `${size}px`;
      star.style.height = `${size}px`;
      star.style.backgroundColor = 'white';
      star.style.borderRadius = '50%';
      star.style.position = 'absolute';
      star.style.opacity = `${gsap.utils.random(0.3, 0.8)}`;

      const x = gsap.utils.random(0, container.offsetWidth);
      const y = gsap.utils.random(0, container.offsetHeight);
      gsap.set(star, { x, y });

      container.appendChild(star);

      // Animate the star
      gsap.to(star, {
        y: y - gsap.utils.random(50, 200),
        opacity: 0,
        duration: gsap.utils.random(3, 6),
        repeat: -1,
        repeatDelay: gsap.utils.random(0, 2),
        ease: 'none'
      });
    }
  };

  /**
   * Animate expertise items with ScrollTrigger
   */
  const animateExpertiseItems = (
    container: Element | null,
    options: ScrollTriggerOptions = {}
  ): gsap.core.Timeline | undefined => {
    if (!container) return undefined;

    const items = container.querySelectorAll('.expertise-item');
    if (!items.length) return undefined;

    // Kill previous ScrollTriggers for expertise items
    ScrollTrigger.getAll().forEach(st => {
      const stTrigger = st.trigger;
      if (stTrigger && typeof (stTrigger as HTMLElement).matches === 'function' &&
          (stTrigger as HTMLElement).matches('.expertise-item')) {
        st.kill();
      }
    });

    // Create a single ScrollTrigger for the container
    const tl = gsap.timeline({
      scrollTrigger: {
        trigger: container,
        start: 'top 75%',
        end: 'bottom 15%',
        toggleActions: 'play none none reverse',
        // markers: true, // Uncomment for debugging
        ...options
      }
    });

    // Add staggered animations to the timeline
    tl.fromTo(items,
      { opacity: 0, y: 50, scale: 0.95 },
      {
        opacity: 1,
        y: 0,
        scale: 1,
        duration: 0.8,
        stagger: {
          amount: 0.6, // Total stagger time
          from: "start",
          grid: "auto",
          ease: "power2.inOut"
        },
        ease: 'back.out(1.2)',
        clearProps: "all" // Improves performance after animation
      }
    );

    return tl;
  };

  /**
   * Generic fade in animation with ScrollTrigger (enhanced)
   */
  const fadeInOnScroll = (
    elements: string | Element | NodeListOf<Element>,
    options: ScrollTriggerOptions & AnimationOptions = {}
  ) => {
    const {
      duration = 0.8,
      stagger = 0.1,
      ease = 'power2.out',
      start = 'top 80%',
      end = 'bottom 20%',
      ...scrollOptions
    } = options;

    let targets: Element[] = [];

    if (typeof elements === 'string') {
      targets = Array.from(document.querySelectorAll(elements));
    } else if (elements instanceof Element) {
      targets = [elements];
    } else if (elements instanceof NodeList) {
      targets = Array.from(elements);
    }

    targets.forEach((element, index) => {
      gsap.fromTo(element,
        { opacity: 0, y: 30, scale: 0.95 },
        {
          opacity: 1,
          y: 0,
          scale: 1,
          duration,
          ease,
          delay: stagger * index,
          scrollTrigger: {
            trigger: element,
            start,
            end,
            toggleActions: 'play none none reverse',
            invalidateOnRefresh: true,
            ...scrollOptions
          }
        }
      );
    });
  };

  /**
   * Enhanced parallax effect for backgrounds and elements
   */
  const createParallaxBackground = (
    element: string | Element,
    options: {
      speed?: number;
      direction?: 'up' | 'down';
      distance?: number;
    } = {}
  ) => {
    const { speed = 0.5, direction = 'up', distance = 100 } = options;

    const target = typeof element === 'string'
      ? document.querySelector(element)
      : element;

    if (!target) return;

    const yMovement = direction === 'up' ? -distance * speed : distance * speed;

    gsap.to(target, {
      y: yMovement,
      ease: 'none',
      scrollTrigger: {
        trigger: target,
        start: 'top bottom',
        end: 'bottom top',
        scrub: true,
        invalidateOnRefresh: true
      }
    });
  };

  /**
   * Cleanup all ScrollTriggers
   */
  const cleanupScrollTriggers = () => {
    ScrollTrigger.getAll().forEach(st => st.kill());
  };

  return {
    animateSkillsIn,
    animateSkillsOut,
    animateHeaderIn,
    animateMastheadIn,
    createStarParticles,
    animateExpertiseItems,
    fadeInOnScroll,
    createParallaxBackground,
    cleanupScrollTriggers
  };
};
