<svg xmlns="http://www.w3.org/2000/svg" width="1200" height="630" viewBox="0 0 1200 630">
  <!-- Background with Gradient -->
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#020315" /> <!-- neutral-1 -->
      <stop offset="50%" stop-color="#4628A6" /> <!-- primary-1 -->
      <stop offset="100%" stop-color="#5D35DD" /> <!-- primary-2 -->
    </linearGradient>
    
    <!-- Favicon Gradient -->
    <linearGradient id="faviconGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#4628A6" /> <!-- primary-1 -->
      <stop offset="50%" stop-color="#5D35DD" /> <!-- primary-2 -->
      <stop offset="100%" stop-color="#AF87FF" /> <!-- primary-3 -->
    </linearGradient>
    
    <!-- Particle Effect Filter -->
    <filter id="noise" x="-50%" y="-50%" width="200%" height="200%">
      <feTurbulence type="fractalNoise" baseFrequency="0.015" numOctaves="2" stitchTiles="stitch" />
      <feDisplacementMap in="SourceGraphic" scale="5" />
    </filter>
    
    <!-- Glow Effect -->
    <filter id="glow" x="-50%" y="-50%" width="200%" height="200%">
      <feGaussianBlur stdDeviation="5" result="blur" />
      <feComposite in="SourceGraphic" in2="blur" operator="over" />
    </filter>
    

  </defs>
  
  <!-- Base Background -->
  <rect width="1200" height="630" fill="url(#bgGradient)" />
  
  <!-- Decorative Elements -->
  <g opacity="0.1" filter="url(#noise)">
    <circle cx="200" cy="200" r="100" fill="#FFFFFF" />
    <circle cx="1000" cy="500" r="150" fill="#FFFFFF" />
    <circle cx="600" cy="100" r="50" fill="#FFFFFF" />
    <circle cx="300" cy="500" r="120" fill="#FFFFFF" />
    <circle cx="900" cy="200" r="90" fill="#FFFFFF" />
  </g>
  
  <!-- Favicon Element (Right Side) -->
  <g transform="translate(850, 215) scale(3)">
    <!-- Base Shape with Rounded Corners -->
    <rect width="32" height="32" rx="6" fill="url(#faviconGradient)" filter="url(#glow)" />
    
    <!-- Decorative Elements -->
    <circle cx="8" cy="8" r="2" fill="rgba(255,255,255,0.3)" filter="url(#noise)" />
    <circle cx="24" cy="24" r="3" fill="rgba(255,255,255,0.2)" filter="url(#noise)" />
    
    <!-- Letter H with Dynamic Style -->
    <g fill="#FFFFFF">
      <!-- Left Vertical Line -->
      <rect x="8" y="7" width="3" height="18" rx="1.5" />
      
      <!-- Right Vertical Line -->
      <rect x="21" y="7" width="3" height="18" rx="1.5" />
      
      <!-- Horizontal Connector -->
      <rect x="8" y="14.5" width="16" height="3" rx="1.5" />
      
      <!-- Decorative Dot -->
      <circle cx="16" cy="6" r="1.5" />
    </g>
  </g>
  
  <!-- Logo/H Letter (Left Side) -->
  <g transform="translate(150, 215)" fill="#FFFFFF">
    <!-- Left Vertical Line -->
    <rect x="0" y="0" width="30" height="200" rx="15" />
    
    <!-- Right Vertical Line -->
    <rect x="170" y="0" width="30" height="200" rx="15" />
    
    <!-- Horizontal Connector -->
    <rect x="0" y="85" width="200" height="30" rx="15" />
  </g>
  
  <!-- Text Content -->
  <g fill="#FFFFFF" font-family="Manrope, sans-serif">
    <!-- Name -->
    <text x="400" y="250" font-size="72" font-weight="700">Huy Nguyen</text>
    
    <!-- Title -->
    <text x="400" y="330" font-size="48" font-weight="600" fill="#AF87FF">Fullstack Developer</text>
    
    <!-- Tagline -->
    <text x="400" y="400" font-size="32" font-weight="400" fill="rgba(255,255,255,0.8)">React • Next.js • Node.js • AWS • TypeScript</text>
    
    <!-- Website -->
    <text x="400" y="470" font-size="28" font-weight="400" fill="rgba(255,255,255,0.6)">huylaptrinhweb.com</text>
  </g>
</svg>