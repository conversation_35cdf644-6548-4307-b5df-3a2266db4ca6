<svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 32 32">
  <!-- Gradient Background -->
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#4628A6" /> <!-- primary-1 -->
      <stop offset="50%" stop-color="#5D35DD" /> <!-- primary-2 -->
      <stop offset="100%" stop-color="#AF87FF" /> <!-- primary-3 -->
    </linearGradient>
    
    <!-- Particle Effect Filter -->
    <filter id="noise" x="-50%" y="-50%" width="200%" height="200%">
      <feTurbulence type="fractalNoise" baseFrequency="0.65" numOctaves="3" stitchTiles="stitch" />
      <feDisplacementMap in="SourceGraphic" scale="2" />
    </filter>
  </defs>
  
  <!-- Base Shape with Rounded Corners -->
  <rect width="32" height="32" rx="6" fill="url(#bgGradient)" />
  
  <!-- Decorative Elements -->
  <circle cx="8" cy="8" r="2" fill="rgba(255,255,255,0.3)" filter="url(#noise)" />
  <circle cx="24" cy="24" r="3" fill="rgba(255,255,255,0.2)" filter="url(#noise)" />
  
  <!-- Letter H with Dynamic Style -->
  <g fill="#FFFFFF">
    <!-- Left Vertical Line -->
    <rect x="8" y="7" width="3" height="18" rx="1.5" />
    
    <!-- Right Vertical Line -->
    <rect x="21" y="7" width="3" height="18" rx="1.5" />
    
    <!-- Horizontal Connector -->
    <rect x="8" y="14.5" width="16" height="3" rx="1.5" />
    
    <!-- Decorative Dot -->
    <circle cx="16" cy="6" r="1.5" />
  </g>
</svg>