<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16">
  <!-- Gradient Background -->
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#4628A6" /> <!-- primary-1 -->
      <stop offset="50%" stop-color="#5D35DD" /> <!-- primary-2 -->
      <stop offset="100%" stop-color="#AF87FF" /> <!-- primary-3 -->
    </linearGradient>
  </defs>
  
  <!-- Base Shape with Rounded Corners -->
  <rect width="16" height="16" rx="3" fill="url(#bgGradient)" />
  
  <!-- Letter H with Dynamic Style -->
  <g fill="#FFFFFF">
    <!-- Left Vertical Line -->
    <rect x="4" y="3.5" width="1.5" height="9" rx="0.75" />
    
    <!-- Right Vertical Line -->
    <rect x="10.5" y="3.5" width="1.5" height="9" rx="0.75" />
    
    <!-- Horizontal Connector -->
    <rect x="4" y="7.25" width="8" height="1.5" rx="0.75" />
  </g>
</svg>