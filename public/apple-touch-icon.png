<svg xmlns="http://www.w3.org/2000/svg" width="180" height="180" viewBox="0 0 180 180">
  <!-- Gradient Background -->
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#4628A6" /> <!-- primary-1 -->
      <stop offset="50%" stop-color="#5D35DD" /> <!-- primary-2 -->
      <stop offset="100%" stop-color="#AF87FF" /> <!-- primary-3 -->
    </linearGradient>
    
    <!-- Particle Effect Filter -->
    <filter id="noise" x="-50%" y="-50%" width="200%" height="200%">
      <feTurbulence type="fractalNoise" baseFrequency="0.65" numOctaves="3" stitchTiles="stitch" />
      <feDisplacementMap in="SourceGraphic" scale="2" />
    </filter>
  </defs>
  
  <!-- Base Shape with Rounded Corners -->
  <rect width="180" height="180" rx="40" fill="url(#bgGradient)" />
  
  <!-- Decorative Elements -->
  <circle cx="45" cy="45" r="10" fill="rgba(255,255,255,0.3)" filter="url(#noise)" />
  <circle cx="135" cy="135" r="15" fill="rgba(255,255,255,0.2)" filter="url(#noise)" />
  
  <!-- Letter H with Dynamic Style -->
  <g fill="#FFFFFF">
    <!-- Left Vertical Line -->
    <rect x="45" y="40" width="18" height="100" rx="9" />
    
    <!-- Right Vertical Line -->
    <rect x="117" y="40" width="18" height="100" rx="9" />
    
    <!-- Horizontal Connector -->
    <rect x="45" y="81" width="90" height="18" rx="9" />
    
    <!-- Decorative Dot -->
    <circle cx="90" cy="30" r="8" />
  </g>
</svg>