import tailwindcss from "@tailwindcss/vite";
// https://nuxt.com/docs/api/configuration/nuxt-config
export default defineNuxtConfig({
  compatibilityDate: "2024-11-01",
  devtools: { enabled: true },
  typescript: {
    strict: true,
    typeCheck: true
  },
  css: ['~/assets/css/main.css'],
  vite: {
    plugins: [
      tailwindcss(),
    ],
  },
  modules: [
    'nuxt-directus'
  ],
  directus: {
    url: process.env.DIRECTUS_URL || 'http://localhost:8055',
    token: process.env.DIRECTUS_TOKEN,
    autoRefresh: true,
    fetchUserParams: {
      fields: ['*']
    }
  },
  vue: {
    compilerOptions: {
      isCustomElement: (tag) => tag.startsWith('swiper-')
    }
  },
  app: {
    head: {
      title: '<PERSON><PERSON>en | Fullstack Developer',
      titleTemplate: '%s - <PERSON><PERSON>',
      meta: [
        { charset: 'utf-8' },
        { name: 'viewport', content: 'width=device-width, initial-scale=1' },
        { name: 'format-detection', content: 'telephone=no' },
        
        // Primary Meta Tags
        { name: 'description', content: '<PERSON><PERSON> is a fullstack developer specializing in modern web applications using Vue.js, React, Node.js, and cloud technologies.' },
        { name: 'author', content: 'Huy Nguyen' },
        { name: 'keywords', content: 'fullstack developer, web developer, vue.js, nuxt.js, react, node.js, typescript, portfolio' },
        
        // Open Graph / Facebook
        { property: 'og:type', content: 'website' },
        { property: 'og:url', content: 'https://huylaptrinhweb.com/' },
        { property: 'og:title', content: 'Huy Nguyen | Fullstack Developer Portfolio' },
        { property: 'og:description', content: 'Huy Nguyen is a fullstack developer specializing in modern web applications using Vue.js, React, Node.js, and cloud technologies.' },
        { property: 'og:image', content: 'https://huylaptrinhweb.com/og-image.png' },
        
        // Twitter
        { name: 'twitter:card', content: 'summary_large_image' },
        { name: 'twitter:url', content: 'https://huylaptrinhweb.com/' },
        { name: 'twitter:title', content: 'Huy Nguyen | Fullstack Developer Portfolio' },
        { name: 'twitter:description', content: 'Huy Nguyen is a fullstack developer specializing in modern web applications using Vue.js, React, Node.js, and cloud technologies.' },
        { name: 'twitter:image', content: 'https://huylaptrinhweb.com/og-image.png' },
        
        // Theme Color
        { name: 'theme-color', content: '#4628A6' }
      ],
      link: [
        // Favicon
        { rel: 'icon', type: 'image/svg+xml', href: '/favicon.svg' },
        { rel: 'icon', type: 'image/png', sizes: '32x32', href: '/favicon-32x32.png' },
        { rel: 'icon', type: 'image/png', sizes: '16x16', href: '/favicon-16x16.png' },
        { rel: 'apple-touch-icon', sizes: '180x180', href: '/apple-touch-icon.png' },
        { rel: 'manifest', href: '/site.webmanifest' },
        
        // Canonical URL
        { rel: 'canonical', href: 'https://huylaptrinhweb.com/' },
        
        // Fonts (if needed)
        { rel: 'preconnect', href: 'https://fonts.googleapis.com' },
        { rel: 'preconnect', href: 'https://fonts.gstatic.com' }
      ]
    }
  }
})
