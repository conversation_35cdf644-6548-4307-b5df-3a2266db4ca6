<template>
  <PageTemplate title="<PERSON><PERSON> | Fullstack Developer">
    <!-- Masthead Section -->
    <Masthead id="about" class="parallax-section" />

    <!-- Skill Listing Section -->
    <SkillListing id="skills" title="Skills" :expertiseGroups="legacyExpertiseDataForSkillListing" class="parallax-section" />

    <!-- Expertise Listing Section -->
    <ExpertiseListing id="expertise" :expertises="transformedExpertiseData" class="parallax-section" />

    <!-- Professional Experience Listing Section -->
    <ProfessionalExperienceListing id="experience" class="parallax-section" />

    <!-- Outstanding Project Listing Section -->
    <OutstandingProjectListing id="projects" :projects="sampleProjects" class="parallax-section" />

    <!-- Education Listing Section -->
    <EducationListing id="education" class="parallax-section" />

  </PageTemplate>
</template>

<script setup lang="ts">
import { onMounted } from 'vue';
import { gsap } from 'gsap';
import { <PERSON>rollTrigger } from 'gsap/ScrollTrigger';

gsap.registerPlugin(ScrollTrigger);
import PageTemplate from '~/components/templates/PageTemplate.vue';
import Masthead from '~/components/organisms/Masthead.vue';
import SkillListing from '~/components/organisms/SkillListing.vue';
import ExpertiseListing from '~/components/organisms/ExpertiseListing.vue';
import ProfessionalExperienceListing from '~/components/organisms/ProfessionalExperienceListing.vue';
import OutstandingProjectListing from '~/components/organisms/OutstandingProjectListing.vue';
import EducationListing from '~/components/organisms/EducationListing.vue';
import { ref, computed } from 'vue';

// Define types for the original expertise data structure
interface OriginalExpertiseItem {
  text: string;
  highlight?: 'neutral_2_50' | 'primary_3_15' | 'primary_2' | 'primary_3_25' | 'neutral_2_bg';
}

interface OriginalExpertiseColumn { // Renamed from OriginalExpertiseGroup
  items: OriginalExpertiseItem[];
}

interface OriginalSlideData {
  columns: OriginalExpertiseColumn[];
}

// Define types for the new component's props (ExpertiseItem is already defined in ExpertiseListing)
// TransformedExpertiseItem is effectively what ExpertiseListing calls ExpertiseItem
interface TransformedExpertiseItem {
  text: string;
  bgColorClass?: string;
  customBgStyle?: string;
}
type TransformedExpertiseColumn = TransformedExpertiseItem[];
type TransformedSlideData = TransformedExpertiseColumn[];

// Sample data for OutstandingProjectListing
interface Project {
  name: string;
  category: string;
  imageUrl: string;
  url: string;
  gradient?: string;
  glassEffect?: boolean;
  overlayColor?: string;
  glassBgColor?: string;
}

const sampleProjects = ref<Project[]>([
  {
    name: 'WerkSG', // Large featured project
    category: 'Website, App, Admin Portal',
    imageUrl: '/images/projects/project-bg-large.png',
    url: 'https://werk.sg/',
    overlayColor: 'rgba(93, 53, 221, 0.8)', // Purple overlay for large card
  },
  {
    name: 'RoccoVideo',
    category: 'Mobile App, Admin Portal',
    imageUrl: '/images/projects/project-bg-radial-glass.png',
    url: 'https://roccovideo.com',
    gradient: `radial-gradient(ellipse at 25% 95%, rgba(2,3,21,0) 40%, rgba(93,53,221,0.7) 70%, #5D35DD 85%, #AF87FF 100%)`,
    glassEffect: true,
  },
  {
    name: 'ProjectSG',
    category: 'Website, Admin Portal, Website Builder',
    imageUrl: '/images/projects/project-bg-overlay.png',
    url: 'https://project.sg',
    overlayColor: 'rgba(93, 53, 221, 0.5)',
  },
  {
    name: 'MaidProfit',
    category: 'Website, App',
    imageUrl: '/images/projects/project-bg-large.png',
    url: 'https://maidprofit.com',
    gradient: `radial-gradient(ellipse at 25% 95%, rgba(2,3,21,0) 40%, rgba(93,53,221,0.7) 70%, #5D35DD 85%, #AF87FF 100%)`,
    glassEffect: true,
  },
  {
    name: 'S&I Ratings',
    category: 'Website',
    imageUrl: '/images/projects/project-bg-overlay.png',
    url: 'https://sniratings.com.vn/',
    overlayColor: 'rgba(93, 53, 221, 0.6)',
  },
  {
    name: 'FPT AI',
    category: 'Website',
    imageUrl: '/images/projects/project-bg-radial-glass.png',
    url: 'https://fpt.ai/',
    overlayColor: 'rgba(93, 53, 221, 0.7)',
    glassEffect: true,
  }
]);

const singleSlideRawData: OriginalExpertiseColumn[] = [
  {
    items: [
      { text: 'Experience with AWS services like EC2, LB, S3, R53, Cloudfront, ECS, VPC, Lambda', highlight: 'neutral_2_50' },
      { text: 'Experience with Tencent / Huawei / Alibaba services like CVM/ECS, LB, CDN, OBS, DNS', highlight: 'primary_3_15' },
      { text: 'Experience with Backend technologies: NodeJS, Express, NestJS, PHP, Laravel' },
    ],
  },
  {
    items: [
      { text: 'Experience with Frontend technologies: HTML5, CSS3, SASS, LESS, SCSS, jQuery, VanillaJS, NextJS, Remix JS, GatsbyJS, TailwindCss, Styled Component, PandaCss, Bootstrap, MUI, Daisy UI, Ant', highlight: 'primary_2' },
      { text: 'Experience with ViteJS, Webpack, Gulp, Rollup, NPM, and Yarn' },
      { text: 'Experience with modular programming and Restful API with JSON payloads, GraphQL' },
    ],
  },
  {
    items: [
      { text: 'Experience with CMS platforms: WordPress, Shopify, Directus, Ghost, Strapi, Contentful, and KeystoneJS', highlight: 'primary_3_25' },
      { text: 'Knowledge of modern highly scalable web architectures including cloud services: AWS, Digital Ocean, Tencent, Huawei, Alibaba, Vultr, MongoAtlas and Google Cloud' },
      { text: 'Experience in Chrome-Extension and data visualisation libraries: D3.js, Highcharts, Chart.js and Rechart', highlight: 'neutral_2_bg' },
    ],
  },
];

// Create multiple slides by cloning the raw data
const secondSlideData: OriginalExpertiseColumn[] = [
  {
    items: [
      { text: 'Experience in database querying and analytics: MongoDB, PostgreSQL, MySQL, Redis', highlight: 'neutral_2_50' },
      { text: 'Experience in code versioning control: Github, Gitlab, Bitbucket', highlight: 'primary_3_15' },
      { text: 'Experience using Unix/Linux, Ubuntu, Apache, Nginx, Let\'s Encrypt, PM2' },
    ],
  },
  {
    items: [
      { text: 'Experience Docker: Container, Volumes, Images', highlight: 'primary_2' },
      { text: 'Experience in developing custom loader or plugin for bundler like vite, rollups, webpack', },
      { text: 'Familiar with the Agile software development process, including Sprints and Scrum' },
    ],
  },
  {
    items: [
      { text: 'Experience in using AI tools: copilot github, chatGPT to initialize the codes as well as improve programming algorithms', highlight: 'primary_3_25' },
      { text: 'Experience to use Google PageSpeed Insights, Chrome DevTools to check, identify problem and improve website performance' },
      { text: 'Experience with CI/CD tools such as Circle CI, Gitlab CI, Github Actions, and Vercel', highlight: 'neutral_2_bg' },
    ],
  },
];

const myExpertiseSlidesData = ref<OriginalSlideData[]>([
  { columns: JSON.parse(JSON.stringify(singleSlideRawData)) }, // Slide 1
  { columns: JSON.parse(JSON.stringify(secondSlideData)) }, // Slide 2 with new data
]);

const legacyExpertiseDataForSkillListing = computed((): OriginalExpertiseColumn[] => {
  return myExpertiseSlidesData.value[0]?.columns || [];
});

const transformedExpertiseData = computed((): TransformedSlideData[] => {
  return myExpertiseSlidesData.value.map(slide => // Iterate over slides
    slide.columns.map(column => // Iterate over columns in a slide
      column.items.map(item => { // Iterate over items in a column
        const transformed: TransformedExpertiseItem = { text: item.text };
        switch (item.highlight) {
          case 'neutral_2_50':
            transformed.customBgStyle = 'background-color: rgba(36, 37, 51, 0.5)';
            break;
          case 'primary_3_15':
            transformed.customBgStyle = 'background-color: rgba(175, 135, 255, 0.15)';
            break;
          case 'primary_2':
            transformed.bgColorClass = 'bg-[var(--color-primary-2)]';
            break;
          case 'primary_3_25':
            transformed.customBgStyle = 'background-color: rgba(175, 135, 255, 0.25)';
            break;
          case 'neutral_2_bg':
            transformed.bgColorClass = 'bg-[var(--color-neutral-2)]';
            break;
        }
        return transformed;
      })
    )
  );
});

onMounted(() => {
  gsap.utils.toArray('.parallax-section').forEach((section: any) => {
    gsap.fromTo(
      section,
      { y: 50, opacity: 0.8 },
      {
        y: 0,
        opacity: 1,
        duration: 0.8,
        ease: 'power2.out',
        scrollTrigger: {
          trigger: section,
          start: 'top 80%', // When the top of the trigger hits 80% of the viewport height
          end: 'bottom 20%', // When the bottom of the trigger hits 20% of the viewport height
          toggleActions: 'play none none reverse', // Play on enter, reverse on leave
          // markers: true, // Uncomment for debugging
        },
      }
    );
  });
});
</script>
