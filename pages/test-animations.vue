<template>
  <PageTemplate title="Animation Test Page">
    <div class="container mx-auto px-4 py-8">
      <h1 class="text-4xl font-bold mb-8 text-center">Animation Test Page</h1>
      
      <!-- Button Tests -->
      <section class="mb-12">
        <h2 class="text-2xl font-semibold mb-6">Button Animations</h2>
        <div class="flex flex-wrap gap-4">
          <Button variant="primary" size="md">Primary Button</Button>
          <Button variant="secondary" size="md">Secondary Button</Button>
          <Button variant="outline" size="md">Outline Button</Button>
          <Button variant="primary" size="lg">Large Button</Button>
        </div>
      </section>

      <!-- Menu Item Tests -->
      <section class="mb-12">
        <h2 class="text-2xl font-semibold mb-6">Menu Item Animations</h2>
        <div class="flex flex-wrap gap-4">
          <MenuItem text="Home" link="#home" />
          <MenuItem text="About" link="#about" selected />
          <MenuItem text="Contact" link="#contact" />
        </div>
      </section>

      <!-- Project Card Tests -->
      <section class="mb-12 parallax-section">
        <h2 class="text-2xl font-semibold mb-6">Project Card Animations</h2>
        <div class="grid md:grid-cols-2 gap-6">
          <div class="project-card bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg p-6 text-white min-h-[200px] flex items-center justify-center">
            <span class="text-xl font-semibold">Test Project Card 1</span>
          </div>
          <div class="project-card bg-gradient-to-br from-green-500 to-teal-600 rounded-lg p-6 text-white min-h-[200px] flex items-center justify-center">
            <span class="text-xl font-semibold">Test Project Card 2</span>
          </div>
        </div>
      </section>

      <!-- Skill Card Tests -->
      <section class="mb-12 parallax-section">
        <h2 class="text-2xl font-semibold mb-6">Skill Card Animations</h2>
        <div class="grid grid-cols-3 md:grid-cols-6 gap-4">
          <SkillCard name="Vue.js" image-url="/images/skills/vue.svg" />
          <SkillCard name="React" image-url="/images/skills/react.svg" />
          <SkillCard name="TypeScript" image-url="/images/skills/typescript.svg" />
          <SkillCard name="Node.js" image-url="/images/skills/nodejs.svg" />
          <SkillCard name="GSAP" image-url="/images/skills/gsap.svg" />
          <SkillCard name="Tailwind" image-url="/images/skills/tailwind.svg" />
        </div>
      </section>

      <!-- Category Button Tests -->
      <section class="mb-12">
        <h2 class="text-2xl font-semibold mb-6">Category Button Animations</h2>
        <div class="flex flex-wrap gap-4">
          <CategoryButton text="All" :is-selected="true" />
          <CategoryButton text="Frontend" />
          <CategoryButton text="Backend" />
          <CategoryButton text="DevOps" />
        </div>
      </section>

      <!-- Scroll Animation Tests -->
      <section class="mb-12 parallax-section">
        <h2 class="text-2xl font-semibold mb-6">Scroll Animation Tests</h2>
        <div class="space-y-8">
          <div class="bg-gray-100 p-8 rounded-lg">
            <h3 class="text-xl font-semibold mb-4">Fade In Animation</h3>
            <p>This section should fade in when scrolled into view.</p>
          </div>
          <div class="bg-blue-100 p-8 rounded-lg">
            <h3 class="text-xl font-semibold mb-4">Slide Up Animation</h3>
            <p>This section should slide up when scrolled into view.</p>
          </div>
          <div class="bg-green-100 p-8 rounded-lg">
            <h3 class="text-xl font-semibold mb-4">Scale Animation</h3>
            <p>This section should scale in when scrolled into view.</p>
          </div>
        </div>
      </section>

      <!-- Loading Animation Tests -->
      <section class="mb-12">
        <h2 class="text-2xl font-semibold mb-6">Loading Animation Tests</h2>
        <div class="space-y-4">
          <div class="bg-gray-200 h-4 rounded animate-pulse"></div>
          <div class="bg-gray-200 h-4 rounded animate-pulse w-3/4"></div>
          <div class="bg-gray-200 h-4 rounded animate-pulse w-1/2"></div>
        </div>
      </section>
    </div>
  </PageTemplate>
</template>

<script setup lang="ts">
import { onMounted } from 'vue';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';
import { useAnimations } from '~/composables/useAnimations';

import PageTemplate from '~/components/templates/PageTemplate.vue';
import Button from '~/components/atoms/Button.vue';
import MenuItem from '~/components/molecules/MenuItem.vue';
import SkillCard from '~/components/molecules/SkillCard.vue';
import CategoryButton from '~/components/molecules/CategoryButton.vue';

gsap.registerPlugin(ScrollTrigger);

const { animateProjectCards, fadeInOnScroll } = useAnimations();

onMounted(() => {
  // Animate project cards
  const projectContainer = document.querySelector('.grid.md\\:grid-cols-2');
  if (projectContainer) {
    animateProjectCards(projectContainer, {
      duration: 0.8,
      stagger: 0.2,
      ease: 'power2.out'
    });
  }

  // Animate parallax sections
  gsap.utils.toArray('.parallax-section').forEach((section: any, index: number) => {
    gsap.fromTo(
      section,
      { y: 60, opacity: 0, scale: 0.95 },
      {
        y: 0,
        opacity: 1,
        scale: 1,
        duration: 1,
        ease: 'power3.out',
        delay: index * 0.1,
        scrollTrigger: {
          trigger: section,
          start: 'top 85%',
          end: 'bottom 15%',
          toggleActions: 'play none none reverse',
          invalidateOnRefresh: true,
        },
      }
    );
  });
});
</script>

<style scoped>
/* Additional test styles */
.project-card {
  transition: all 0.3s ease-out;
}

.project-card:hover {
  transform: translateY(-4px) scale(1.02);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}
</style>
